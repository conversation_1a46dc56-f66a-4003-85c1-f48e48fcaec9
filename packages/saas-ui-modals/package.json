{"name": "@saas-ui/modals", "version": "3.0.0-next.40", "description": "A modal manager for Chakra UI", "type": "module", "exports": {".": {"sui": "./src/index.ts", "types": "./dist/index.d.ts", "require": "./dist/index.cjs", "import": "./dist/index.js"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "tsup --config tsup.config.ts", "lint": "eslint src --ext .ts,.tsx,.js,.jsx --config ../../eslint.config.js", "lint:staged": "lint-staged --allow-empty --config ../../lint-staged.config.js", "typecheck": "tsc --noEmit"}, "files": ["dist"], "sideEffects": false, "publishConfig": {"access": "public"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://saas-ui.dev/", "repository": {"type": "git", "url": "git+https://github.com/saas-js/saas-ui.git", "directory": "packages/saas-ui-modals"}, "keywords": ["react", "ui", "chakra-ui", "design-system", "react-components", "uikit", "accessible", "components", "emotion", "library", "modal"], "storybook": {"title": "Saas UI", "url": "https://storybook.saas-ui.dev"}, "dependencies": {"@saas-ui/core": "workspace:*", "@saas-ui/forms": "workspace:*", "@saas-ui/hooks": "workspace:*", "@saas-ui/modals-provider": "workspace:*"}, "peerDependencies": {"@chakra-ui/react": "^3.20.0", "@emotion/react": ">=11.0.0", "@saas-ui/react": "workspace:*", "react": ">=18.0.0", "react-dom": ">=18.0.0"}, "devDependencies": {"yup": "^1.6.1", "zod": "^3.24.2", "typescript": "^5.8.3", "react": "^19.0.0", "react-dom": "^19.0.0", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4"}}