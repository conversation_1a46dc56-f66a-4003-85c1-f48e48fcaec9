{"name": "@saas-ui/supabase", "version": "3.0.0-next.1", "description": "Saas UI Supabase Auth integration", "exports": {".": {"sui": "./src/index.ts", "require": "./dist/index.js", "types": "./dist/index.d.ts", "import": "./dist/index.mjs"}}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"clean": "rimraf --no-glob ./dist", "build": "tsup src/index.ts --config tsup.config.ts", "lint": "eslint src --ext .ts,.tsx,.js,.jsx --config ../../eslint.config.js", "lint:staged": "lint-staged --allow-empty --config ../../lint-staged.config.js", "typecheck": "tsc --noEmit"}, "files": ["dist"], "sideEffects": false, "publishConfig": {"access": "public"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://saas-ui.dev/", "repository": {"type": "git", "url": "git+https://github.com/saas-js/saas-ui.git", "directory": "packages/saas-ui-auth"}, "keywords": ["react", "ui", "chakra-ui", "design-system", "react-components", "uikit", "accessible", "components", "emotion", "library", "authentication", "supabase"], "storybook": {"title": "Saas UI", "url": "https://storybook.saas-ui.dev"}, "dependencies": {"@saas-ui/auth-provider": "workspace:*"}, "peerDependencies": {"@supabase/supabase-js": "^2.45.4", "react": ">=18.0.0", "react-dom": ">=18.0.0"}, "devDependencies": {"@supabase/supabase-js": "^2.49.2", "typescript": "^5.8.3", "react": "^19.0.0", "react-dom": "^19.0.0", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4"}}