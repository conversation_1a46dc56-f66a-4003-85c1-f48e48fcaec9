{"name": "@saas-ui/forms", "version": "3.0.0-next.40", "description": "Fully functional forms for Chakra UI.", "exports": {".": {"sui": "./src/index.ts", "require": "./dist/index.js", "types": "./dist/index.d.ts", "import": "./dist/index.mjs"}, "./yup": {"sui": "./dist/yup/index.ts", "require": "./dist/yup/index.js", "types": "./dist/yup/index.d.ts", "import": "./dist/yup/index.mjs"}, "./zod": {"sui": "./dist/zod/index.ts", "require": "./dist/zod/index.js", "types": "./dist/zod/index.d.ts", "import": "./dist/zod/index.mjs"}}, "typesVersions": {"*": {"yup": ["./dist/yup/index.d.ts"], "zod": ["./dist/zod/index.d.ts"]}}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"clean": "rimraf --no-glob ./dist", "build": "pnpm build:package && pnpm build:yup && pnpm build:zod", "build:package": "tsup src/index.ts --config tsup.config.ts", "build:yup": "tsup yup/src/index.ts --config yup/tsup.config.ts", "build:zod": "tsup zod/src/index.ts --config zod/tsup.config.ts", "lint": "eslint src --ext .ts,.tsx,.js,.jsx --config ../../eslint.config.js", "lint:staged": "lint-staged --allow-empty --config ../../lint-staged.config.js", "typecheck": "tsc --noEmit", "tsd": "tsd"}, "files": ["dist"], "sideEffects": false, "publishConfig": {"access": "public"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://saas-ui.dev/", "repository": {"type": "git", "url": "git+https://github.com/saas-js/saas-ui.git", "directory": "packages/saas-ui-forms"}, "keywords": ["react", "ui", "chakra-ui", "design-system", "react-components", "uikit", "accessible", "components", "emotion", "library", "design-system", "forms", "react-hook-form"], "storybook": {"title": "Saas UI", "url": "https://storybook.saas-ui.dev"}, "dependencies": {"@hookform/resolvers": "^5.0.0", "@saas-ui/core": "workspace:*", "@saas-ui/react": "workspace:*", "@standard-schema/spec": "^1.0.0", "react-hook-form": "^7.55.0", "zod": "^3.24.2"}, "peerDependencies": {"@chakra-ui/react": "^3.20.0", "@emotion/react": ">=11.0.0", "react": ">=18.0.0", "react-dom": ">=18.0.0"}, "devDependencies": {"tsd": "^0.31.2", "tsup": "^8.4.0", "zod": "^3.24.2", "typescript": "^5.8.3", "react": "^19.0.0", "react-dom": "^19.0.0", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4"}, "tsd": {"directory": "tests"}}