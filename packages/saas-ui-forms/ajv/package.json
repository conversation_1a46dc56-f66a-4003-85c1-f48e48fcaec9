{"name": "ajv-resolver", "description": "Saas UI Forms field resolver: ajv", "version": "1.0.0", "private": true, "exports": {".": {"require": "./../dist/ajv/index.js", "import": "./../dist/ajv/index.mjs"}, "./src": {"default": "./src/index.ts"}}, "source": "./src/index.ts", "main": "../dist/ajv/index.js", "module": "../dist/ajv/index.mjs", "types": "../dist/ajv/index.d.ts", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "peerDependencies": {"@hookform/resolvers": "^3.0.0", "react-hook-form": "^7.43.9", "ajv": "^8.12.0", "ajv-errors": "^3.0.0", "json-schema-to-ts": "^2.7.2"}}