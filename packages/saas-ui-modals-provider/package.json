{"name": "@saas-ui/modals-provider", "version": "1.0.0-next.1", "description": "A UI library agnostic modal manager for React", "exports": {".": {"sui": "./src/index.ts", "types": "./dist/index.d.ts", "require": "./dist/index.js", "import": "./dist/index.mjs"}}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "tsup --config tsup.config.ts", "lint": "eslint src --ext .ts,.tsx,.js,.jsx --config ../../eslint.config.js", "lint:staged": "lint-staged --allow-empty --config ../../lint-staged.config.js", "typecheck": "tsc --noEmit"}, "files": ["dist"], "sideEffects": false, "publishConfig": {"access": "public"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://saas-ui.dev/", "repository": {"type": "git", "url": "git+https://github.com/saas-js/saas-ui.git", "directory": "packages/saas-ui-modals-provider"}, "keywords": ["react", "ui", "design-system", "react-components", "uikit", "accessible", "components", "library", "modals", "dialogs"], "storybook": {"title": "Saas UI", "url": "https://storybook.saas-ui.dev"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "devDependencies": {"typescript": "^5.8.3", "react": "^19.0.0", "react-dom": "^19.0.0", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4"}}