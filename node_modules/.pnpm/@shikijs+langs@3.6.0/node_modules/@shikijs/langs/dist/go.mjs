const lang = Object.freeze(JSON.parse("{\"displayName\":\"Go\",\"name\":\"go\",\"patterns\":[{\"include\":\"#statements\"}],\"repository\":{\"after_control_variables\":{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations-without-brackets\"},{\"match\":\"\\\\[\",\"name\":\"punctuation.definition.begin.bracket.square.go\"},{\"match\":\"]\",\"name\":\"punctuation.definition.end.bracket.square.go\"},{\"match\":\"\\\\w+\",\"name\":\"variable.other.go\"}]}},\"match\":\"(?<=\\\\brange\\\\b|\\\\bswitch\\\\b|;|\\\\bif\\\\b|\\\\bfor\\\\b|[<>]|<=|>=|==|!=|\\\\w[-%*+/]|\\\\w[-%*+/]=|\\\\|\\\\||&&)\\\\s*((?![]\\\\[]+)[-\\\\]!%*+./:<=>\\\\[_[:alnum:]]+)\\\\s*(?=\\\\{)\"},\"brackets\":{\"patterns\":[{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.begin.bracket.curly.go\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.curly.go\"}},\"patterns\":[{\"include\":\"$self\"}]},{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.begin.bracket.round.go\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.round.go\"}},\"patterns\":[{\"include\":\"$self\"}]},{\"begin\":\"\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.begin.bracket.square.go\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.square.go\"}},\"patterns\":[{\"include\":\"$self\"}]}]},\"built_in_functions\":{\"patterns\":[{\"match\":\"\\\\b(append|cap|close|complex|copy|delete|imag|len|panic|print|println|real|recover|min|max|clear)\\\\b(?=\\\\()\",\"name\":\"entity.name.function.support.builtin.go\"},{\"begin\":\"\\\\b(new)\\\\b(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"entity.name.function.support.builtin.go\"},\"2\":{\"name\":\"punctuation.definition.begin.bracket.round.go\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.round.go\"}},\"patterns\":[{\"include\":\"#functions\"},{\"include\":\"#struct_variables_types\"},{\"include\":\"#type-declarations\"},{\"include\":\"#generic_types\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"},{\"include\":\"$self\"}]},{\"begin\":\"\\\\b(make)\\\\b(\\\\()((?:(?:[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+(?:\\\\([^)]+\\\\))?)?[]*\\\\[]+{0,1}(?:(?!\\\\bmap\\\\b)[.\\\\w]+)?(\\\\[(?:\\\\S+(?:,\\\\s*\\\\S+)*)?])?,?)?\",\"beginCaptures\":{\"1\":{\"name\":\"entity.name.function.support.builtin.go\"},\"2\":{\"name\":\"punctuation.definition.begin.bracket.round.go\"},\"3\":{\"patterns\":[{\"include\":\"#type-declarations-without-brackets\"},{\"include\":\"#parameter-variable-types\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.round.go\"}},\"patterns\":[{\"include\":\"$self\"}]}]},\"comments\":{\"patterns\":[{\"begin\":\"(/\\\\*)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.comment.go\"}},\"end\":\"(\\\\*/)\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.comment.go\"}},\"name\":\"comment.block.go\"},{\"begin\":\"(//)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.comment.go\"}},\"end\":\"\\\\n|$\",\"name\":\"comment.line.double-slash.go\"}]},\"const_assignment\":{\"patterns\":[{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#delimiters\"},{\"match\":\"\\\\w+\",\"name\":\"variable.other.constant.go\"}]},\"2\":{\"patterns\":[{\"include\":\"#type-declarations-without-brackets\"},{\"include\":\"#generic_types\"},{\"match\":\"\\\\(\",\"name\":\"punctuation.definition.begin.bracket.round.go\"},{\"match\":\"\\\\)\",\"name\":\"punctuation.definition.end.bracket.round.go\"},{\"match\":\"\\\\[\",\"name\":\"punctuation.definition.begin.bracket.square.go\"},{\"match\":\"]\",\"name\":\"punctuation.definition.end.bracket.square.go\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"(?<=\\\\bconst\\\\b)\\\\s*\\\\b([.\\\\w]+(?:,\\\\s*[.\\\\w]+)*)\\\\s*((?:(?:[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+(?:\\\\([^)]+\\\\))?)?(?![]*\\\\[]+{0,1}\\\\b(?:struct|func|map)\\\\b)(?:[]*.\\\\[\\\\w]+(?:,\\\\s*[]*.\\\\[\\\\w]+)*)?\\\\s*=?)?\"},{\"begin\":\"(?<=\\\\bconst\\\\b)\\\\s*(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.begin.bracket.round.go\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.round.go\"}},\"patterns\":[{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#delimiters\"},{\"match\":\"\\\\w+\",\"name\":\"variable.other.constant.go\"}]},\"2\":{\"patterns\":[{\"include\":\"#type-declarations-without-brackets\"},{\"include\":\"#generic_types\"},{\"match\":\"\\\\(\",\"name\":\"punctuation.definition.begin.bracket.round.go\"},{\"match\":\"\\\\)\",\"name\":\"punctuation.definition.end.bracket.round.go\"},{\"match\":\"\\\\[\",\"name\":\"punctuation.definition.begin.bracket.square.go\"},{\"match\":\"]\",\"name\":\"punctuation.definition.end.bracket.square.go\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"^\\\\s*\\\\b([.\\\\w]+(?:,\\\\s*[.\\\\w]+)*)\\\\s*((?:(?:[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+(?:\\\\([^)]+\\\\))?)?(?![]*\\\\[]+{0,1}\\\\b(?:struct|func|map)\\\\b)(?:[]*.\\\\[\\\\w]+(?:,\\\\s*[]*.\\\\[\\\\w]+)*)?\\\\s*=?)?\"},{\"include\":\"$self\"}]}]},\"delimiters\":{\"patterns\":[{\"match\":\",\",\"name\":\"punctuation.other.comma.go\"},{\"match\":\"\\\\.(?!\\\\.\\\\.)\",\"name\":\"punctuation.other.period.go\"},{\"match\":\":(?!=)\",\"name\":\"punctuation.other.colon.go\"}]},\"double_parentheses_types\":{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations-without-brackets\"},{\"match\":\"\\\\(\",\"name\":\"punctuation.definition.begin.bracket.round.go\"},{\"match\":\"\\\\)\",\"name\":\"punctuation.definition.end.bracket.round.go\"},{\"match\":\"\\\\[\",\"name\":\"punctuation.definition.begin.bracket.square.go\"},{\"match\":\"]\",\"name\":\"punctuation.definition.end.bracket.square.go\"},{\"match\":\"\\\\{\",\"name\":\"punctuation.definition.begin.bracket.curly.go\"},{\"match\":\"}\",\"name\":\"punctuation.definition.end.bracket.curly.go\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"(?<!\\\\w)(\\\\([]*\\\\[]+{0,1}[.\\\\w]+(?:\\\\[(?:[]*.\\\\[{}\\\\w]+(?:,\\\\s*[]*.\\\\[{}\\\\w]+)*)?])?\\\\))(?=\\\\()\"},\"field_hover\":{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"variable.other.property.go\"}]},\"2\":{\"patterns\":[{\"match\":\"\\\\binvalid\\\\b\\\\s+\\\\btype\\\\b\",\"name\":\"invalid.field.go\"},{\"include\":\"#type-declarations-without-brackets\"},{\"include\":\"#parameter-variable-types\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"(?<=^\\\\bfield\\\\b)\\\\s+([*.\\\\w]+)\\\\s+([\\\\s\\\\S]+)\"},\"function_declaration\":{\"begin\":\"^\\\\b(func)\\\\b\\\\s*(\\\\([^)]+\\\\)\\\\s*)?(?:(\\\\w+)(?=[(\\\\[]))?\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.function.go\"},\"2\":{\"patterns\":[{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.begin.bracket.round.go\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.round.go\"}},\"patterns\":[{\"captures\":{\"1\":{\"name\":\"variable.parameter.go\"},\"2\":{\"patterns\":[{\"include\":\"#type-declarations-without-brackets\"},{\"include\":\"#parameter-variable-types\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"(\\\\w+\\\\s+)?([*.\\\\w]+(?:\\\\[(?:[*.\\\\w]+(?:,\\\\s+)?)+{0,1}])?)\"},{\"include\":\"$self\"}]}]},\"3\":{\"patterns\":[{\"match\":\"\\\\d\\\\w*\",\"name\":\"invalid.illegal.identifier.go\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.function.go\"}]}},\"end\":\"(?<=\\\\))\\\\s*((?:[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+{0,1}(?![]*\\\\[]+{0,1}\\\\b(?:struct|interface)\\\\b)[-\\\\]*.\\\\[\\\\w]+)?\\\\s*(?=\\\\{)\",\"endCaptures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations-without-brackets\"},{\"include\":\"#parameter-variable-types\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"patterns\":[{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.begin.bracket.round.go\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.round.go\"}},\"patterns\":[{\"include\":\"#function_param_types\"}]},{\"begin\":\"([*.\\\\w]+)?(\\\\[)\",\"beginCaptures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]},\"2\":{\"name\":\"punctuation.definition.begin.bracket.square.go\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.square.go\"}},\"patterns\":[{\"include\":\"#generic_param_types\"}]},{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations-without-brackets\"},{\"include\":\"#parameter-variable-types\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"(?<=\\\\))\\\\s*((?:\\\\s*[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+{0,1}[-\\\\]*.<>\\\\[\\\\w]+\\\\s*(?:/[*/].*)?)$\"},{\"include\":\"$self\"}]},\"function_param_types\":{\"patterns\":[{\"include\":\"#struct_variables_types\"},{\"include\":\"#interface_variables_types\"},{\"include\":\"#type-declarations-without-brackets\"},{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"variable.parameter.go\"}]}},\"match\":\"((?:\\\\b\\\\w+,\\\\s*)+{0,1}\\\\b\\\\w+)\\\\s+(?=(?:\\\\s*[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+{0,1}[]*\\\\[]+{0,1}\\\\b(?:struct|interface)\\\\b\\\\s*\\\\{)\"},{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"variable.parameter.go\"}]}},\"match\":\"(?:(?<=\\\\()|^\\\\s*)((?:\\\\b\\\\w+,\\\\s*)+(?:/[*/].*)?)$\"},{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#delimiters\"},{\"match\":\"\\\\w+\",\"name\":\"variable.parameter.go\"}]},\"2\":{\"patterns\":[{\"include\":\"#type-declarations-without-brackets\"},{\"include\":\"#parameter-variable-types\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"((?:\\\\b\\\\w+,\\\\s*)+{0,1}\\\\b\\\\w+)\\\\s+((?:\\\\s*[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+{0,1}(?:[]*.\\\\[\\\\w]+{0,1}(?:\\\\bfunc\\\\b\\\\([^)]+{0,1}\\\\)(?:\\\\s*[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+{0,1}\\\\s*)+(?:[]*.\\\\[\\\\w]+|\\\\([^)]+{0,1}\\\\))?|(?:[]*\\\\[]+{0,1}[*.\\\\w]+(?:\\\\[[^]]+])?[*.\\\\w]+{0,1})+))\"},{\"begin\":\"([*.\\\\w]+)?(\\\\[)\",\"beginCaptures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]},\"2\":{\"name\":\"punctuation.definition.begin.bracket.square.go\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.square.go\"}},\"patterns\":[{\"include\":\"#generic_param_types\"}]},{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.begin.bracket.round.go\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.round.go\"}},\"patterns\":[{\"include\":\"#function_param_types\"}]},{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"([.\\\\w]+)\"},{\"include\":\"$self\"}]},\"functions\":{\"begin\":\"\\\\b(func)\\\\b(?=\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.function.go\"}},\"end\":\"(?<=\\\\))(\\\\s*(?:[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+)?(\\\\s*(?:[]*\\\\[]+{0,1}[*.\\\\w]+)?(?:\\\\[(?:[*.\\\\w]+{0,1}(?:\\\\[[^]]+{0,1}])?(?:,\\\\s+)?)+]|\\\\([^)]+{0,1}\\\\))?[*.\\\\w]+{0,1}\\\\s*(?=\\\\{)|\\\\s*(?:[]*\\\\[]+{0,1}(?!\\\\bfunc\\\\b)[*.\\\\w]+(?:\\\\[(?:[*.\\\\w]+{0,1}(?:\\\\[[^]]+{0,1}])?(?:,\\\\s+)?)+])?[*.\\\\w]+{0,1}|\\\\([^)]+{0,1}\\\\)))?\",\"endCaptures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"}]},\"2\":{\"patterns\":[{\"include\":\"#type-declarations-without-brackets\"},{\"include\":\"#parameter-variable-types\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"patterns\":[{\"include\":\"#parameter-variable-types\"}]},\"functions_inline\":{\"captures\":{\"1\":{\"name\":\"keyword.function.go\"},\"2\":{\"patterns\":[{\"include\":\"#type-declarations-without-brackets\"},{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.begin.bracket.round.go\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.round.go\"}},\"patterns\":[{\"include\":\"#function_param_types\"},{\"include\":\"$self\"}]},{\"match\":\"\\\\[\",\"name\":\"punctuation.definition.begin.bracket.square.go\"},{\"match\":\"]\",\"name\":\"punctuation.definition.end.bracket.square.go\"},{\"match\":\"\\\\{\",\"name\":\"punctuation.definition.begin.bracket.curly.go\"},{\"match\":\"}\",\"name\":\"punctuation.definition.end.bracket.curly.go\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"\\\\b(func)\\\\b(\\\\([^/]*?\\\\)\\\\s+\\\\([^/]*?\\\\))\\\\s+(?=\\\\{)\"},\"generic_param_types\":{\"patterns\":[{\"include\":\"#struct_variables_types\"},{\"include\":\"#interface_variables_types\"},{\"include\":\"#type-declarations-without-brackets\"},{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"variable.parameter.go\"}]}},\"match\":\"((?:\\\\b\\\\w+,\\\\s*)+{0,1}\\\\b\\\\w+)\\\\s+(?=(?:\\\\s*[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+{0,1}[]*\\\\[]+{0,1}\\\\b(?:struct|interface)\\\\b\\\\s*\\\\{)\"},{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"variable.parameter.go\"}]}},\"match\":\"(?:(?<=\\\\()|^\\\\s*)((?:\\\\b\\\\w+,\\\\s*)+(?:/[*/].*)?)$\"},{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#delimiters\"},{\"match\":\"\\\\w+\",\"name\":\"variable.parameter.go\"}]},\"2\":{\"patterns\":[{\"include\":\"#type-declarations-without-brackets\"},{\"include\":\"#parameter-variable-types\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]},\"3\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"((?:\\\\b\\\\w+,\\\\s*)+{0,1}\\\\b\\\\w+)\\\\s+((?:\\\\s*[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+{0,1}(?:[]*.\\\\[\\\\w]+{0,1}(?:\\\\bfunc\\\\b\\\\([^)]+{0,1}\\\\)(?:\\\\s*[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+{0,1}\\\\s*)+(?:[*.\\\\w]+|\\\\([^)]+{0,1}\\\\))?|(?:(?:[*.~\\\\w]+|\\\\[(?:[*.\\\\w]+{0,1}(?:\\\\[[^]]+{0,1}])?(?:,\\\\s+)?)+])[*.\\\\w]+{0,1})+))\"},{\"begin\":\"([*.\\\\w]+)?(\\\\[)\",\"beginCaptures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]},\"2\":{\"name\":\"punctuation.definition.begin.bracket.square.go\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.square.go\"}},\"patterns\":[{\"include\":\"#generic_param_types\"}]},{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.begin.bracket.round.go\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.round.go\"}},\"patterns\":[{\"include\":\"#function_param_types\"}]},{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"\\\\b([.\\\\w]+)\"},{\"include\":\"$self\"}]},\"generic_types\":{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]},\"2\":{\"patterns\":[{\"include\":\"#parameter-variable-types\"}]}},\"match\":\"([*.\\\\w]+)(\\\\[[^]]+{0,1}])\"},\"group-functions\":{\"patterns\":[{\"include\":\"#function_declaration\"},{\"include\":\"#functions_inline\"},{\"include\":\"#functions\"},{\"include\":\"#built_in_functions\"},{\"include\":\"#support_functions\"}]},\"group-types\":{\"patterns\":[{\"include\":\"#other_struct_interface_expressions\"},{\"include\":\"#type_assertion_inline\"},{\"include\":\"#struct_variables_types\"},{\"include\":\"#interface_variables_types\"},{\"include\":\"#single_type\"},{\"include\":\"#multi_types\"},{\"include\":\"#struct_interface_declaration\"},{\"include\":\"#double_parentheses_types\"},{\"include\":\"#switch_types\"},{\"include\":\"#type-declarations\"}]},\"group-variables\":{\"patterns\":[{\"include\":\"#const_assignment\"},{\"include\":\"#var_assignment\"},{\"include\":\"#variable_assignment\"},{\"include\":\"#label_loop_variables\"},{\"include\":\"#slice_index_variables\"},{\"include\":\"#property_variables\"},{\"include\":\"#switch_select_case_variables\"},{\"include\":\"#other_variables\"}]},\"import\":{\"patterns\":[{\"begin\":\"\\\\b(import)\\\\s+\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.import.go\"}},\"end\":\"(?!\\\\G)\",\"patterns\":[{\"include\":\"#imports\"}]}]},\"imports\":{\"patterns\":[{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#delimiters\"},{\"match\":\"\\\\w+\",\"name\":\"variable.other.import.go\"}]},\"2\":{\"name\":\"string.quoted.double.go\"},\"3\":{\"name\":\"punctuation.definition.string.begin.go\"},\"4\":{\"name\":\"entity.name.import.go\"},\"5\":{\"name\":\"punctuation.definition.string.end.go\"}},\"match\":\"(\\\\s*[.\\\\w]+)?\\\\s*((\\\")([^\\\"]*)(\\\"))\"},{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.imports.begin.bracket.round.go\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.imports.end.bracket.round.go\"}},\"patterns\":[{\"include\":\"#comments\"},{\"include\":\"#imports\"}]},{\"include\":\"$self\"}]},\"interface_variables_types\":{\"begin\":\"\\\\b(interface)\\\\b\\\\s*(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.interface.go\"},\"2\":{\"name\":\"punctuation.definition.begin.bracket.curly.go\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.curly.go\"}},\"patterns\":[{\"include\":\"#interface_variables_types_field\"},{\"include\":\"$self\"}]},\"interface_variables_types_field\":{\"patterns\":[{\"include\":\"#support_functions\"},{\"include\":\"#type-declarations-without-brackets\"},{\"begin\":\"([*.\\\\w]+)?(\\\\[)\",\"beginCaptures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]},\"2\":{\"name\":\"punctuation.definition.begin.bracket.square.go\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.square.go\"}},\"patterns\":[{\"include\":\"#generic_param_types\"}]},{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.begin.bracket.round.go\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.round.go\"}},\"patterns\":[{\"include\":\"#function_param_types\"}]},{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"([.\\\\w]+)\"}]},\"keywords\":{\"patterns\":[{\"match\":\"\\\\b(break|case|continue|default|defer|else|fallthrough|for|go|goto|if|range|return|select|switch)\\\\b\",\"name\":\"keyword.control.go\"},{\"match\":\"\\\\bchan\\\\b\",\"name\":\"keyword.channel.go\"},{\"match\":\"\\\\bconst\\\\b\",\"name\":\"keyword.const.go\"},{\"match\":\"\\\\bvar\\\\b\",\"name\":\"keyword.var.go\"},{\"match\":\"\\\\bfunc\\\\b\",\"name\":\"keyword.function.go\"},{\"match\":\"\\\\binterface\\\\b\",\"name\":\"keyword.interface.go\"},{\"match\":\"\\\\bmap\\\\b\",\"name\":\"keyword.map.go\"},{\"match\":\"\\\\bstruct\\\\b\",\"name\":\"keyword.struct.go\"},{\"match\":\"\\\\bimport\\\\b\",\"name\":\"keyword.control.import.go\"},{\"match\":\"\\\\btype\\\\b\",\"name\":\"keyword.type.go\"}]},\"label_loop_variables\":{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"variable.other.label.go\"}]}},\"match\":\"^(\\\\s*\\\\w+:\\\\s*|\\\\s*\\\\b(?:break|goto|continue)\\\\b\\\\s+\\\\w+(?:\\\\s*/[*/]\\\\s*.*)?)$\"},\"language_constants\":{\"captures\":{\"1\":{\"name\":\"constant.language.boolean.go\"},\"2\":{\"name\":\"constant.language.null.go\"},\"3\":{\"name\":\"constant.language.iota.go\"}},\"match\":\"\\\\b(?:(true|false)|(nil)|(iota))\\\\b\"},\"map_types\":{\"begin\":\"\\\\b(map)\\\\b(\\\\[)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.map.go\"},\"2\":{\"name\":\"punctuation.definition.begin.bracket.square.go\"}},\"end\":\"(])((?:[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+{0,1}(?![]*\\\\[]+{0,1}\\\\b(?:func|struct|map)\\\\b)[]*\\\\[]+{0,1}[.\\\\w]+(?:\\\\[(?:[]*.\\\\[{}\\\\w]+(?:,\\\\s*[]*.\\\\[{}\\\\w]+)*)?])?)?\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.end.bracket.square.go\"},\"2\":{\"patterns\":[{\"include\":\"#type-declarations-without-brackets\"},{\"match\":\"\\\\[\",\"name\":\"punctuation.definition.begin.bracket.square.go\"},{\"match\":\"]\",\"name\":\"punctuation.definition.end.bracket.square.go\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"patterns\":[{\"include\":\"#type-declarations-without-brackets\"},{\"include\":\"#parameter-variable-types\"},{\"include\":\"#functions\"},{\"match\":\"\\\\[\",\"name\":\"punctuation.definition.begin.bracket.square.go\"},{\"match\":\"]\",\"name\":\"punctuation.definition.end.bracket.square.go\"},{\"match\":\"\\\\{\",\"name\":\"punctuation.definition.begin.bracket.curly.go\"},{\"match\":\"}\",\"name\":\"punctuation.definition.end.bracket.curly.go\"},{\"match\":\"\\\\(\",\"name\":\"punctuation.definition.begin.bracket.round.go\"},{\"match\":\"\\\\)\",\"name\":\"punctuation.definition.end.bracket.round.go\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]},\"multi_types\":{\"begin\":\"\\\\b(type)\\\\b\\\\s*(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.type.go\"},\"2\":{\"name\":\"punctuation.definition.begin.bracket.round.go\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.round.go\"}},\"patterns\":[{\"include\":\"#struct_variables_types\"},{\"include\":\"#interface_variables_types\"},{\"include\":\"#type-declarations-without-brackets\"},{\"include\":\"#parameter-variable-types\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]},\"numeric_literals\":{\"captures\":{\"0\":{\"patterns\":[{\"begin\":\"(?=.)\",\"end\":\"\\\\n|$\",\"patterns\":[{\"captures\":{\"1\":{\"name\":\"constant.numeric.decimal.go\",\"patterns\":[{\"match\":\"(?<=\\\\h)_(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric.go\"}]},\"2\":{\"name\":\"punctuation.separator.constant.numeric.go\"},\"3\":{\"name\":\"constant.numeric.decimal.point.go\"},\"4\":{\"name\":\"constant.numeric.decimal.go\",\"patterns\":[{\"match\":\"(?<=\\\\h)_(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric.go\"}]},\"5\":{\"name\":\"punctuation.separator.constant.numeric.go\"},\"6\":{\"name\":\"keyword.other.unit.exponent.decimal.go\"},\"7\":{\"name\":\"keyword.operator.plus.exponent.decimal.go\"},\"8\":{\"name\":\"keyword.operator.minus.exponent.decimal.go\"},\"9\":{\"name\":\"constant.numeric.exponent.decimal.go\",\"patterns\":[{\"match\":\"(?<=\\\\h)_(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric.go\"}]},\"10\":{\"name\":\"keyword.other.unit.imaginary.go\"},\"11\":{\"name\":\"constant.numeric.decimal.go\",\"patterns\":[{\"match\":\"(?<=\\\\h)_(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric.go\"}]},\"12\":{\"name\":\"punctuation.separator.constant.numeric.go\"},\"13\":{\"name\":\"keyword.other.unit.exponent.decimal.go\"},\"14\":{\"name\":\"keyword.operator.plus.exponent.decimal.go\"},\"15\":{\"name\":\"keyword.operator.minus.exponent.decimal.go\"},\"16\":{\"name\":\"constant.numeric.exponent.decimal.go\",\"patterns\":[{\"match\":\"(?<=\\\\h)_(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric.go\"}]},\"17\":{\"name\":\"keyword.other.unit.imaginary.go\"},\"18\":{\"name\":\"constant.numeric.decimal.point.go\"},\"19\":{\"name\":\"constant.numeric.decimal.go\",\"patterns\":[{\"match\":\"(?<=\\\\h)_(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric.go\"}]},\"20\":{\"name\":\"punctuation.separator.constant.numeric.go\"},\"21\":{\"name\":\"keyword.other.unit.exponent.decimal.go\"},\"22\":{\"name\":\"keyword.operator.plus.exponent.decimal.go\"},\"23\":{\"name\":\"keyword.operator.minus.exponent.decimal.go\"},\"24\":{\"name\":\"constant.numeric.exponent.decimal.go\",\"patterns\":[{\"match\":\"(?<=\\\\h)_(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric.go\"}]},\"25\":{\"name\":\"keyword.other.unit.imaginary.go\"},\"26\":{\"name\":\"keyword.other.unit.hexadecimal.go\"},\"27\":{\"name\":\"constant.numeric.hexadecimal.go\",\"patterns\":[{\"match\":\"(?<=\\\\h)_(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric.go\"}]},\"28\":{\"name\":\"punctuation.separator.constant.numeric.go\"},\"29\":{\"name\":\"constant.numeric.hexadecimal.go\"},\"30\":{\"name\":\"constant.numeric.hexadecimal.go\",\"patterns\":[{\"match\":\"(?<=\\\\h)_(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric.go\"}]},\"31\":{\"name\":\"punctuation.separator.constant.numeric.go\"},\"32\":{\"name\":\"keyword.other.unit.exponent.hexadecimal.go\"},\"33\":{\"name\":\"keyword.operator.plus.exponent.hexadecimal.go\"},\"34\":{\"name\":\"keyword.operator.minus.exponent.hexadecimal.go\"},\"35\":{\"name\":\"constant.numeric.exponent.hexadecimal.go\",\"patterns\":[{\"match\":\"(?<=\\\\h)_(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric.go\"}]},\"36\":{\"name\":\"keyword.other.unit.imaginary.go\"},\"37\":{\"name\":\"keyword.other.unit.hexadecimal.go\"},\"38\":{\"name\":\"constant.numeric.hexadecimal.go\",\"patterns\":[{\"match\":\"(?<=\\\\h)_(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric.go\"}]},\"39\":{\"name\":\"punctuation.separator.constant.numeric.go\"},\"40\":{\"name\":\"keyword.other.unit.exponent.hexadecimal.go\"},\"41\":{\"name\":\"keyword.operator.plus.exponent.hexadecimal.go\"},\"42\":{\"name\":\"keyword.operator.minus.exponent.hexadecimal.go\"},\"43\":{\"name\":\"constant.numeric.exponent.hexadecimal.go\",\"patterns\":[{\"match\":\"(?<=\\\\h)_(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric.go\"}]},\"44\":{\"name\":\"keyword.other.unit.imaginary.go\"},\"45\":{\"name\":\"keyword.other.unit.hexadecimal.go\"},\"46\":{\"name\":\"constant.numeric.hexadecimal.go\"},\"47\":{\"name\":\"constant.numeric.hexadecimal.go\",\"patterns\":[{\"match\":\"(?<=\\\\h)_(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric.go\"}]},\"48\":{\"name\":\"punctuation.separator.constant.numeric.go\"},\"49\":{\"name\":\"keyword.other.unit.exponent.hexadecimal.go\"},\"50\":{\"name\":\"keyword.operator.plus.exponent.hexadecimal.go\"},\"51\":{\"name\":\"keyword.operator.minus.exponent.hexadecimal.go\"},\"52\":{\"name\":\"constant.numeric.exponent.hexadecimal.go\",\"patterns\":[{\"match\":\"(?<=\\\\h)_(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric.go\"}]},\"53\":{\"name\":\"keyword.other.unit.imaginary.go\"}},\"match\":\"\\\\G(?:(?:(?:(?:(?:(?=[.0-9])(?!0[BOXbox])([0-9](?:[0-9]|((?<=\\\\h)_(?=\\\\h)))*)((?<=[0-9])\\\\.|\\\\.(?=[0-9]))([0-9](?:[0-9]|((?<=\\\\h)_(?=\\\\h)))*)?(?:(?<!_)([Ee])(\\\\+?)(-?)([0-9](?:[0-9]|(?<=\\\\h)_(?=\\\\h))*))?(i(?!\\\\w))?(?:\\\\n|$)|(?=[.0-9])(?!0[BOXbox])([0-9](?:[0-9]|((?<=\\\\h)_(?=\\\\h)))*)(?<!_)([Ee])(\\\\+?)(-?)([0-9](?:[0-9]|(?<=\\\\h)_(?=\\\\h))*)(i(?!\\\\w))?(?:\\\\n|$))|((?<=[0-9])\\\\.|\\\\.(?=[0-9]))([0-9](?:[0-9]|((?<=\\\\h)_(?=\\\\h)))*)(?:(?<!_)([Ee])(\\\\+?)(-?)([0-9](?:[0-9]|(?<=\\\\h)_(?=\\\\h))*))?(i(?!\\\\w))?(?:\\\\n|$))|(0[Xx])_?(\\\\h(?:\\\\h|((?<=\\\\h)_(?=\\\\h)))*)((?<=\\\\h)\\\\.|\\\\.(?=\\\\h))(\\\\h(?:\\\\h|((?<=\\\\h)_(?=\\\\h)))*)?(?<!_)([Pp])(\\\\+?)(-?)([0-9](?:[0-9]|(?<=\\\\h)_(?=\\\\h))*)(i(?!\\\\w))?(?:\\\\n|$))|(0[Xx])_?(\\\\h(?:\\\\h|((?<=\\\\h)_(?=\\\\h)))*)(?<!_)([Pp])(\\\\+?)(-?)([0-9](?:[0-9]|(?<=\\\\h)_(?=\\\\h))*)(i(?!\\\\w))?(?:\\\\n|$))|(0[Xx])((?<=\\\\h)\\\\.|\\\\.(?=\\\\h))(\\\\h(?:\\\\h|((?<=\\\\h)_(?=\\\\h)))*)(?<!_)([Pp])(\\\\+?)(-?)([0-9](?:[0-9]|(?<=\\\\h)_(?=\\\\h))*)(i(?!\\\\w))?(?:\\\\n|$))\"},{\"captures\":{\"1\":{\"name\":\"constant.numeric.decimal.go\",\"patterns\":[{\"match\":\"(?<=\\\\h)_(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric.go\"}]},\"2\":{\"name\":\"punctuation.separator.constant.numeric.go\"},\"3\":{\"name\":\"keyword.other.unit.imaginary.go\"},\"4\":{\"name\":\"keyword.other.unit.binary.go\"},\"5\":{\"name\":\"constant.numeric.binary.go\",\"patterns\":[{\"match\":\"(?<=\\\\h)_(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric.go\"}]},\"6\":{\"name\":\"punctuation.separator.constant.numeric.go\"},\"7\":{\"name\":\"keyword.other.unit.imaginary.go\"},\"8\":{\"name\":\"keyword.other.unit.octal.go\"},\"9\":{\"name\":\"constant.numeric.octal.go\",\"patterns\":[{\"match\":\"(?<=\\\\h)_(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric.go\"}]},\"10\":{\"name\":\"punctuation.separator.constant.numeric.go\"},\"11\":{\"name\":\"keyword.other.unit.imaginary.go\"},\"12\":{\"name\":\"keyword.other.unit.hexadecimal.go\"},\"13\":{\"name\":\"constant.numeric.hexadecimal.go\",\"patterns\":[{\"match\":\"(?<=\\\\h)_(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric.go\"}]},\"14\":{\"name\":\"punctuation.separator.constant.numeric.go\"},\"15\":{\"name\":\"keyword.other.unit.imaginary.go\"}},\"match\":\"\\\\G(?:(?:(?:(?=[.0-9])(?!0[BOXbox])([0-9](?:[0-9]|((?<=\\\\h)_(?=\\\\h)))*)(i(?!\\\\w))?(?:\\\\n|$)|(0[Bb])_?([01](?:[01]|((?<=\\\\h)_(?=\\\\h)))*)(i(?!\\\\w))?(?:\\\\n|$))|(0[Oo]?)_?((?:[0-7]|((?<=\\\\h)_(?=\\\\h)))+)(i(?!\\\\w))?(?:\\\\n|$))|(0[Xx])_?(\\\\h(?:\\\\h|((?<=\\\\h)_(?=\\\\h)))*)(i(?!\\\\w))?(?:\\\\n|$))\"},{\"match\":\"(?:[.0-9A-Z_a-z]|(?<=[EPep])[-+])+\",\"name\":\"invalid.illegal.constant.numeric.go\"}]}]}},\"match\":\"(?<!\\\\w)\\\\.?\\\\d(?:[.0-9A-Z_a-z]|(?<=[EPep])[-+])*\"},\"operators\":{\"patterns\":[{\"match\":\"(?<!\\\\w)[\\\\&*]+(?!\\\\d)(?=[]\\\\[\\\\w]|<-)\",\"name\":\"keyword.operator.address.go\"},{\"match\":\"<-\",\"name\":\"keyword.operator.channel.go\"},{\"match\":\"--\",\"name\":\"keyword.operator.decrement.go\"},{\"match\":\"\\\\+\\\\+\",\"name\":\"keyword.operator.increment.go\"},{\"match\":\"(==|!=|<=|>=|<(?!<)|>(?!>))\",\"name\":\"keyword.operator.comparison.go\"},{\"match\":\"(&&|\\\\|\\\\||!)\",\"name\":\"keyword.operator.logical.go\"},{\"match\":\"((?:|[-%*+/:^|]|<<|>>|&\\\\^?)=)\",\"name\":\"keyword.operator.assignment.go\"},{\"match\":\"([-%*+/])\",\"name\":\"keyword.operator.arithmetic.go\"},{\"match\":\"(&(?!\\\\^)|[\\\\^|]|&\\\\^|<<|>>|~)\",\"name\":\"keyword.operator.arithmetic.bitwise.go\"},{\"match\":\"\\\\.\\\\.\\\\.\",\"name\":\"keyword.operator.ellipsis.go\"}]},\"other_struct_interface_expressions\":{\"patterns\":[{\"include\":\"#after_control_variables\"},{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]},\"2\":{\"patterns\":[{\"include\":\"#type-declarations-without-brackets\"},{\"match\":\"\\\\[\",\"name\":\"punctuation.definition.begin.bracket.square.go\"},{\"match\":\"]\",\"name\":\"punctuation.definition.end.bracket.square.go\"},{\"match\":\"\\\\{\",\"name\":\"punctuation.definition.begin.bracket.curly.go\"},{\"match\":\"}\",\"name\":\"punctuation.definition.end.bracket.curly.go\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"\\\\b([.\\\\w]+)(\\\\[(?:[]*.\\\\[{}\\\\w]+(?:,\\\\s*[]*.\\\\[{}\\\\w]+)*)?])?(?=\\\\{)(?<!\\\\b(?:struct|interface)\\\\b)\"}]},\"other_variables\":{\"match\":\"\\\\w+\",\"name\":\"variable.other.go\"},\"package_name\":{\"patterns\":[{\"begin\":\"\\\\b(package)\\\\s+\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.package.go\"}},\"end\":\"(?!\\\\G)\",\"patterns\":[{\"match\":\"\\\\d\\\\w*\",\"name\":\"invalid.illegal.identifier.go\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.package.go\"}]}]},\"parameter-variable-types\":{\"patterns\":[{\"match\":\"\\\\{\",\"name\":\"punctuation.definition.begin.bracket.curly.go\"},{\"match\":\"}\",\"name\":\"punctuation.definition.end.bracket.curly.go\"},{\"begin\":\"([*.\\\\w]+)?(\\\\[)\",\"beginCaptures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]},\"2\":{\"name\":\"punctuation.definition.begin.bracket.square.go\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.square.go\"}},\"patterns\":[{\"include\":\"#generic_param_types\"}]},{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.begin.bracket.round.go\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.round.go\"}},\"patterns\":[{\"include\":\"#function_param_types\"}]}]},\"property_variables\":{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"variable.other.property.go\"}]}},\"match\":\"\\\\b([.\\\\w]+:(?!=))\"},\"raw_string_literals\":{\"begin\":\"`\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.go\"}},\"end\":\"`\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.go\"}},\"name\":\"string.quoted.raw.go\",\"patterns\":[{\"include\":\"#string_placeholder\"}]},\"runes\":{\"patterns\":[{\"begin\":\"'\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.go\"}},\"end\":\"'\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.go\"}},\"name\":\"string.quoted.rune.go\",\"patterns\":[{\"match\":\"\\\\G(\\\\\\\\([0-7]{3}|[\\\"'\\\\\\\\abfnrtv]|x\\\\h{2}|u\\\\h{4}|U\\\\h{8})|.)(?=')\",\"name\":\"constant.other.rune.go\"},{\"match\":\"[^']+\",\"name\":\"invalid.illegal.unknown-rune.go\"}]}]},\"single_type\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"keyword.type.go\"},\"2\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]},\"3\":{\"patterns\":[{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.begin.bracket.round.go\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.round.go\"}},\"patterns\":[{\"include\":\"#function_param_types\"},{\"include\":\"$self\"}]},{\"include\":\"#type-declarations\"},{\"include\":\"#generic_types\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"^\\\\s*\\\\b(type)\\\\b\\\\s*([*.\\\\w]+)\\\\s+(?!(?:=\\\\s*)?[]*\\\\[]+{0,1}\\\\b(?:struct|interface)\\\\b)([\\\\s\\\\S]+)\"},{\"begin\":\"(?:^|\\\\s+)\\\\b(type)\\\\b\\\\s*([*.\\\\w]+)(?=\\\\[)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.type.go\"},\"2\":{\"patterns\":[{\"include\":\"#type-declarations-without-brackets\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"end\":\"(?<=])(\\\\s+(?:=\\\\s*)?(?:[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+{0,1}(?![]*\\\\[]+{0,1}\\\\b(?:struct|interface|func)\\\\b)[-\\\\]*.\\\\[\\\\w]+(?:,\\\\s*[]*.\\\\[\\\\w]+)*)?\",\"endCaptures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations-without-brackets\"},{\"match\":\"\\\\[\",\"name\":\"punctuation.definition.begin.bracket.square.go\"},{\"match\":\"]\",\"name\":\"punctuation.definition.end.bracket.square.go\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"patterns\":[{\"include\":\"#struct_variables_types\"},{\"include\":\"#type-declarations-without-brackets\"},{\"include\":\"#parameter-variable-types\"},{\"match\":\"\\\\[\",\"name\":\"punctuation.definition.begin.bracket.square.go\"},{\"match\":\"]\",\"name\":\"punctuation.definition.end.bracket.square.go\"},{\"match\":\"\\\\{\",\"name\":\"punctuation.definition.begin.bracket.curly.go\"},{\"match\":\"}\",\"name\":\"punctuation.definition.end.bracket.curly.go\"},{\"match\":\"\\\\(\",\"name\":\"punctuation.definition.begin.bracket.round.go\"},{\"match\":\"\\\\)\",\"name\":\"punctuation.definition.end.bracket.round.go\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}]},\"slice_index_variables\":{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"variable.other.go\"}]}},\"match\":\"(?<=\\\\w\\\\[)((?:\\\\b[-%\\\\&*+./<>|\\\\w]+:|:\\\\b[-%\\\\&*+./<>|\\\\w]+)(?:\\\\b[-%\\\\&*+./<>|\\\\w]+)?(?::\\\\b[-%\\\\&*+./<>|\\\\w]+)?)(?=])\"},\"statements\":{\"patterns\":[{\"include\":\"#package_name\"},{\"include\":\"#import\"},{\"include\":\"#syntax_errors\"},{\"include\":\"#group-functions\"},{\"include\":\"#group-types\"},{\"include\":\"#group-variables\"},{\"include\":\"#field_hover\"}]},\"storage_types\":{\"patterns\":[{\"match\":\"\\\\bbool\\\\b\",\"name\":\"storage.type.boolean.go\"},{\"match\":\"\\\\bbyte\\\\b\",\"name\":\"storage.type.byte.go\"},{\"match\":\"\\\\berror\\\\b\",\"name\":\"storage.type.error.go\"},{\"match\":\"\\\\b(complex(64|128)|float(32|64)|u?int(8|16|32|64)?)\\\\b\",\"name\":\"storage.type.numeric.go\"},{\"match\":\"\\\\brune\\\\b\",\"name\":\"storage.type.rune.go\"},{\"match\":\"\\\\bstring\\\\b\",\"name\":\"storage.type.string.go\"},{\"match\":\"\\\\buintptr\\\\b\",\"name\":\"storage.type.uintptr.go\"},{\"match\":\"\\\\bany\\\\b\",\"name\":\"entity.name.type.any.go\"},{\"match\":\"\\\\bcomparable\\\\b\",\"name\":\"entity.name.type.comparable.go\"}]},\"string_escaped_char\":{\"patterns\":[{\"match\":\"\\\\\\\\([0-7]{3}|[\\\"'\\\\\\\\abfnrtv]|x\\\\h{2}|u\\\\h{4}|U\\\\h{8})\",\"name\":\"constant.character.escape.go\"},{\"match\":\"\\\\\\\\[^\\\"'0-7Uabfnrtuvx]\",\"name\":\"invalid.illegal.unknown-escape.go\"}]},\"string_literals\":{\"patterns\":[{\"begin\":\"\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.go\"}},\"end\":\"\\\"\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.go\"}},\"name\":\"string.quoted.double.go\",\"patterns\":[{\"include\":\"#string_escaped_char\"},{\"include\":\"#string_placeholder\"}]}]},\"string_placeholder\":{\"patterns\":[{\"match\":\"%(\\\\[\\\\d+])?([- #+0]{0,2}((\\\\d+|\\\\*)?(\\\\.?(\\\\d+|\\\\*|(\\\\[\\\\d+])\\\\*?)?(\\\\[\\\\d+])?)?))?[%EFGTUXb-gopqstvwx]\",\"name\":\"constant.other.placeholder.go\"}]},\"struct_interface_declaration\":{\"captures\":{\"1\":{\"name\":\"keyword.type.go\"},\"2\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"^\\\\s*\\\\b(type)\\\\b\\\\s*([.\\\\w]+)\"},\"struct_variable_types_fields_multi\":{\"patterns\":[{\"begin\":\"(\\\\w+(?:,\\\\s*\\\\w+)*(?:\\\\s*[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+{0,1}\\\\s+[]*\\\\[]+{0,1})\\\\b(struct)\\\\b\\\\s*(\\\\{)\",\"beginCaptures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"variable.other.property.go\"}]},\"2\":{\"name\":\"keyword.struct.go\"},\"3\":{\"name\":\"punctuation.definition.begin.bracket.curly.go\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.curly.go\"}},\"patterns\":[{\"include\":\"#struct_variables_types_fields\"},{\"include\":\"$self\"}]},{\"begin\":\"(\\\\w+(?:,\\\\s*\\\\w+)*(?:\\\\s*[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+{0,1}\\\\s+[]*\\\\[]+{0,1})\\\\b(interface)\\\\b\\\\s*(\\\\{)\",\"beginCaptures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"variable.other.property.go\"}]},\"2\":{\"name\":\"keyword.interface.go\"},\"3\":{\"name\":\"punctuation.definition.begin.bracket.curly.go\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.curly.go\"}},\"patterns\":[{\"include\":\"#interface_variables_types_field\"},{\"include\":\"$self\"}]},{\"begin\":\"(\\\\w+(?:,\\\\s*\\\\w+)*(?:\\\\s*[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+{0,1}\\\\s+[]*\\\\[]+{0,1})\\\\b(func)\\\\b\\\\s*(\\\\()\",\"beginCaptures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"variable.other.property.go\"}]},\"2\":{\"name\":\"keyword.function.go\"},\"3\":{\"name\":\"punctuation.definition.begin.bracket.round.go\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.round.go\"}},\"patterns\":[{\"include\":\"#function_param_types\"},{\"include\":\"$self\"}]},{\"include\":\"#parameter-variable-types\"}]},\"struct_variables_types\":{\"begin\":\"\\\\b(struct)\\\\b\\\\s*(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.struct.go\"},\"2\":{\"name\":\"punctuation.definition.begin.bracket.curly.go\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.curly.go\"}},\"patterns\":[{\"include\":\"#struct_variables_types_fields\"},{\"include\":\"$self\"}]},\"struct_variables_types_fields\":{\"patterns\":[{\"include\":\"#struct_variable_types_fields_multi\"},{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"(?<=\\\\{)\\\\s*((?:\\\\s*[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+{0,1}[]*.\\\\[\\\\w]+)\\\\s*(?=})\"},{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"variable.other.property.go\"}]},\"2\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"(?<=\\\\{)\\\\s*((?:\\\\w+,\\\\s*)+{0,1}\\\\w+\\\\s+)((?:\\\\s*[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+{0,1}[]*.\\\\[\\\\w]+)\\\\s*(?=})\"},{\"captures\":{\"1\":{\"patterns\":[{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"variable.other.property.go\"}]},\"2\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"((?:\\\\w+,\\\\s*)+{0,1}\\\\w+\\\\s+)?((?:\\\\s*[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+{0,1}\\\\S+;?)\"}]}},\"match\":\"(?<=\\\\{)((?:\\\\s*(?:(?:\\\\w+,\\\\s*)+{0,1}\\\\w+\\\\s+)?(?:\\\\s*[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+{0,1}\\\\S+;?)+)\\\\s*(?=})\"},{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"((?:\\\\s*[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+{0,1}[*.\\\\w]+\\\\s*)(?:(?=[\\\"/`])|$)\"},{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"variable.other.property.go\"}]},\"2\":{\"patterns\":[{\"include\":\"#type-declarations-without-brackets\"},{\"include\":\"#parameter-variable-types\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"((?:\\\\w+,\\\\s*)+{0,1}\\\\w+\\\\s+)([^\\\"/`]+)\"}]},\"support_functions\":{\"captures\":{\"1\":{\"name\":\"entity.name.function.support.go\"},\"2\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\d\\\\w*\",\"name\":\"invalid.illegal.identifier.go\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.function.support.go\"}]},\"3\":{\"patterns\":[{\"include\":\"#type-declarations-without-brackets\"},{\"match\":\"\\\\[\",\"name\":\"punctuation.definition.begin.bracket.square.go\"},{\"match\":\"]\",\"name\":\"punctuation.definition.end.bracket.square.go\"},{\"match\":\"\\\\{\",\"name\":\"punctuation.definition.begin.bracket.curly.go\"},{\"match\":\"}\",\"name\":\"punctuation.definition.end.bracket.curly.go\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"(?:((?<=\\\\.)\\\\b\\\\w+)|\\\\b(\\\\w+))(\\\\[(?:[]\\\"'*.\\\\[{}\\\\w]+(?:,\\\\s*[]*.\\\\[{}\\\\w]+)*)?])?(?=\\\\()\"},\"switch_select_case_variables\":{\"captures\":{\"1\":{\"name\":\"keyword.control.go\"},\"2\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"include\":\"#support_functions\"},{\"include\":\"#variable_assignment\"},{\"match\":\"\\\\w+\",\"name\":\"variable.other.go\"}]}},\"match\":\"^\\\\s*\\\\b(case)\\\\b\\\\s+([\\\\s\\\\S]+:\\\\s*(?:/[*/].*)?)$\"},\"switch_types\":{\"begin\":\"(?<=\\\\bswitch\\\\b)\\\\s*(\\\\w+\\\\s*:=)?\\\\s*([-\\\\]%\\\\&(-+./<>\\\\[|\\\\w]+)(\\\\.\\\\(\\\\btype\\\\b\\\\)\\\\s*)(\\\\{)\",\"beginCaptures\":{\"1\":{\"patterns\":[{\"include\":\"#operators\"},{\"match\":\"\\\\w+\",\"name\":\"variable.other.assignment.go\"}]},\"2\":{\"patterns\":[{\"include\":\"#support_functions\"},{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"variable.other.go\"}]},\"3\":{\"patterns\":[{\"include\":\"#delimiters\"},{\"include\":\"#brackets\"},{\"match\":\"\\\\btype\\\\b\",\"name\":\"keyword.type.go\"}]},\"4\":{\"name\":\"punctuation.definition.begin.bracket.curly.go\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.curly.go\"}},\"patterns\":[{\"captures\":{\"1\":{\"name\":\"keyword.control.go\"},\"2\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]},\"3\":{\"name\":\"punctuation.other.colon.go\"},\"4\":{\"patterns\":[{\"include\":\"#comments\"}]}},\"match\":\"^\\\\s*\\\\b(case)\\\\b\\\\s+([!*,.<=>\\\\w\\\\s]+)(:)(\\\\s*/[*/]\\\\s*.*)?$\"},{\"begin\":\"\\\\bcase\\\\b\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.control.go\"}},\"end\":\":\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.other.colon.go\"}},\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]},{\"include\":\"$self\"}]},\"syntax_errors\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"invalid.illegal.slice.go\"}},\"match\":\"\\\\[](\\\\s+)\"},{\"match\":\"\\\\b0[0-7]*[89]\\\\d*\\\\b\",\"name\":\"invalid.illegal.numeric.go\"}]},\"terminators\":{\"match\":\";\",\"name\":\"punctuation.terminator.go\"},\"type-declarations\":{\"patterns\":[{\"include\":\"#language_constants\"},{\"include\":\"#comments\"},{\"include\":\"#map_types\"},{\"include\":\"#brackets\"},{\"include\":\"#delimiters\"},{\"include\":\"#keywords\"},{\"include\":\"#operators\"},{\"include\":\"#runes\"},{\"include\":\"#storage_types\"},{\"include\":\"#raw_string_literals\"},{\"include\":\"#string_literals\"},{\"include\":\"#numeric_literals\"},{\"include\":\"#terminators\"}]},\"type-declarations-without-brackets\":{\"patterns\":[{\"include\":\"#language_constants\"},{\"include\":\"#comments\"},{\"include\":\"#map_types\"},{\"include\":\"#delimiters\"},{\"include\":\"#keywords\"},{\"include\":\"#operators\"},{\"include\":\"#runes\"},{\"include\":\"#storage_types\"},{\"include\":\"#raw_string_literals\"},{\"include\":\"#string_literals\"},{\"include\":\"#numeric_literals\"},{\"include\":\"#terminators\"}]},\"type_assertion_inline\":{\"captures\":{\"1\":{\"name\":\"keyword.type.go\"},\"2\":{\"patterns\":[{\"include\":\"#type-declarations\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"(?<=\\\\.\\\\()(?:\\\\b(type)\\\\b|((?:\\\\s*[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+{0,1}[]*.\\\\[\\\\w]+))(?=\\\\))\"},\"var_assignment\":{\"patterns\":[{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#delimiters\"},{\"match\":\"\\\\w+\",\"name\":\"variable.other.assignment.go\"}]},\"2\":{\"patterns\":[{\"include\":\"#type-declarations-without-brackets\"},{\"include\":\"#generic_types\"},{\"match\":\"\\\\(\",\"name\":\"punctuation.definition.begin.bracket.round.go\"},{\"match\":\"\\\\)\",\"name\":\"punctuation.definition.end.bracket.round.go\"},{\"match\":\"\\\\[\",\"name\":\"punctuation.definition.begin.bracket.square.go\"},{\"match\":\"]\",\"name\":\"punctuation.definition.end.bracket.square.go\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"(?<=\\\\bvar\\\\b)\\\\s*\\\\b([.\\\\w]+(?:,\\\\s*[.\\\\w]+)*)\\\\s*((?:(?:[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+(?:\\\\([^)]+\\\\))?)?(?![]*\\\\[]+{0,1}\\\\b(?:struct|func|map)\\\\b)(?:[]*.\\\\[\\\\w]+(?:,\\\\s*[]*.\\\\[\\\\w]+)*)?\\\\s*=?)?\"},{\"begin\":\"(?<=\\\\bvar\\\\b)\\\\s*(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.begin.bracket.round.go\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.round.go\"}},\"patterns\":[{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#delimiters\"},{\"match\":\"\\\\w+\",\"name\":\"variable.other.assignment.go\"}]},\"2\":{\"patterns\":[{\"include\":\"#type-declarations-without-brackets\"},{\"include\":\"#generic_types\"},{\"match\":\"\\\\(\",\"name\":\"punctuation.definition.begin.bracket.round.go\"},{\"match\":\"\\\\)\",\"name\":\"punctuation.definition.end.bracket.round.go\"},{\"match\":\"\\\\[\",\"name\":\"punctuation.definition.begin.bracket.square.go\"},{\"match\":\"]\",\"name\":\"punctuation.definition.end.bracket.square.go\"},{\"match\":\"\\\\w+\",\"name\":\"entity.name.type.go\"}]}},\"match\":\"^\\\\s*\\\\b([.\\\\w]+(?:,\\\\s*[.\\\\w]+)*)\\\\s*((?:(?:[]*\\\\[]+{0,1}(?:<-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*<-)?\\\\s*)+(?:\\\\([^)]+\\\\))?)?(?![]*\\\\[]+{0,1}\\\\b(?:struct|func|map)\\\\b)(?:[]*.\\\\[\\\\w]+(?:,\\\\s*[]*.\\\\[\\\\w]+)*)?\\\\s*=?)?\"},{\"include\":\"$self\"}]}]},\"variable_assignment\":{\"patterns\":[{\"captures\":{\"0\":{\"patterns\":[{\"include\":\"#delimiters\"},{\"match\":\"\\\\d\\\\w*\",\"name\":\"invalid.illegal.identifier.go\"},{\"match\":\"\\\\w+\",\"name\":\"variable.other.assignment.go\"}]}},\"match\":\"\\\\b\\\\w+(?:,\\\\s*\\\\w+)*(?=\\\\s*:=)\"},{\"captures\":{\"0\":{\"patterns\":[{\"include\":\"#delimiters\"},{\"include\":\"#operators\"},{\"match\":\"\\\\d\\\\w*\",\"name\":\"invalid.illegal.identifier.go\"},{\"match\":\"\\\\w+\",\"name\":\"variable.other.assignment.go\"}]}},\"match\":\"\\\\b[*.\\\\w]+(?:,\\\\s*[*.\\\\w]+)*(?=\\\\s*=(?!=))\"}]}},\"scopeName\":\"source.go\"}"))

export default [
lang
]
