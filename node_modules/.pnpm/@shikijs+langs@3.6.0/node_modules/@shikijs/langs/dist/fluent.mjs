const lang = Object.freeze(JSON.parse("{\"displayName\":\"Fluent\",\"name\":\"fluent\",\"patterns\":[{\"include\":\"#comment\"},{\"include\":\"#message\"},{\"include\":\"#wrong-line\"}],\"repository\":{\"attributes\":{\"begin\":\"\\\\s*(\\\\.[A-Za-z][-0-9A-Z_a-z]*\\\\s*=\\\\s*)\",\"beginCaptures\":{\"1\":{\"name\":\"support.class.attribute-begin.fluent\"}},\"end\":\"^(?=\\\\s*[^.])\",\"patterns\":[{\"include\":\"#placeable\"}]},\"comment\":{\"match\":\"^##?#?\\\\s.*$\",\"name\":\"comment.fluent\"},\"function-comma\":{\"match\":\",\",\"name\":\"support.function.function-comma.fluent\"},\"function-named-argument\":{\"begin\":\"([0-9A-Za-z]+:)\\\\s*([\\\"0-9A-Za-z]+)\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.named-argument.name.fluent\"},\"2\":{\"name\":\"variable.other.named-argument.value.fluent\"}},\"end\":\"(?=[),\\\\s])\",\"name\":\"variable.other.named-argument.fluent\"},\"function-positional-argument\":{\"match\":\"\\\\$[-0-9A-Z_a-z]+\",\"name\":\"variable.other.function.positional-argument.fluent\"},\"invalid-placeable-string-missing-end-quote\":{\"match\":\"\\\"[^\\\"]+$\",\"name\":\"invalid.illegal.wrong-placeable-missing-end-quote.fluent\"},\"invalid-placeable-wrong-placeable-missing-end\":{\"match\":\"([^A-Z}]*|[^-][^>])$\\\\b\",\"name\":\"invalid.illegal.wrong-placeable-missing-end.fluent\"},\"message\":{\"begin\":\"^(-?[A-Za-z][-0-9A-Z_a-z]*\\\\s*=\\\\s*)\",\"beginCaptures\":{\"1\":{\"name\":\"support.class.message-identifier.fluent\"}},\"contentName\":\"string.fluent\",\"end\":\"^(?=\\\\S)\",\"patterns\":[{\"include\":\"#attributes\"},{\"include\":\"#placeable\"}]},\"placeable\":{\"begin\":\"(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.placeable.begin.fluent\"}},\"contentName\":\"variable.other.placeable.content.fluent\",\"end\":\"(})\",\"endCaptures\":{\"1\":{\"name\":\"keyword.placeable.end.fluent\"}},\"patterns\":[{\"include\":\"#placeable-string\"},{\"include\":\"#placeable-function\"},{\"include\":\"#placeable-reference-or-number\"},{\"include\":\"#selector\"},{\"include\":\"#invalid-placeable-wrong-placeable-missing-end\"},{\"include\":\"#invalid-placeable-string-missing-end-quote\"},{\"include\":\"#invalid-placeable-wrong-function-name\"}]},\"placeable-function\":{\"begin\":\"([A-Z][-0-9A-Z_]*\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.placeable-function.call.begin.fluent\"}},\"contentName\":\"string.placeable-function.fluent\",\"end\":\"(\\\\))\",\"endCaptures\":{\"1\":{\"name\":\"support.function.placeable-function.call.end.fluent\"}},\"patterns\":[{\"include\":\"#function-comma\"},{\"include\":\"#function-positional-argument\"},{\"include\":\"#function-named-argument\"}]},\"placeable-reference-or-number\":{\"match\":\"(([-$])[-0-9A-Z_a-z]+|[A-Za-z][-0-9A-Z_a-z]*|[0-9]+)\",\"name\":\"variable.other.placeable.reference-or-number.fluent\"},\"placeable-string\":{\"begin\":\"(\\\")(?=[^\\\\n]*\\\")\",\"beginCaptures\":{\"1\":{\"name\":\"variable.other.placeable-string-begin.fluent\"}},\"contentName\":\"string.placeable-string-content.fluent\",\"end\":\"(\\\")\",\"endCaptures\":{\"1\":{\"name\":\"variable.other.placeable-string-end.fluent\"}}},\"selector\":{\"begin\":\"(->)\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.selector.begin.fluent\"}},\"contentName\":\"string.selector.content.fluent\",\"end\":\"^(?=\\\\s*})\",\"patterns\":[{\"include\":\"#selector-item\"}]},\"selector-item\":{\"begin\":\"(\\\\s*\\\\*?\\\\[)([-0-9A-Z_a-z]+)(]\\\\s*)\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.selector-item.begin.fluent\"},\"2\":{\"name\":\"variable.other.selector-item.begin.fluent\"},\"3\":{\"name\":\"support.function.selector-item.begin.fluent\"}},\"contentName\":\"string.selector-item.content.fluent\",\"end\":\"^(?=(\\\\s*})|(\\\\s*\\\\[)|(\\\\s*\\\\*))\",\"patterns\":[{\"include\":\"#placeable\"}]},\"wrong-line\":{\"match\":\".*\",\"name\":\"invalid.illegal.wrong-line.fluent\"}},\"scopeName\":\"source.ftl\",\"aliases\":[\"ftl\"]}"))

export default [
lang
]
