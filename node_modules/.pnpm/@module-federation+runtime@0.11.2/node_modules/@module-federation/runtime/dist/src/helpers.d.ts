import { getGlobalFederationInstance } from './utils';
export type { IGlobalUtils, IShareUtils, } from '@module-federation/runtime-core';
declare const _default: {
    global: {
        getGlobalFederationInstance: typeof getGlobalFederationInstance;
        Global: typeof import("@module-federation/runtime-core").Global;
        nativeGlobal: typeof global;
        resetFederationGlobalInfo: typeof import("@module-federation/runtime-core").resetFederationGlobalInfo;
        setGlobalFederationInstance: typeof import("@module-federation/runtime-core").setGlobalFederationInstance;
        getGlobalFederationConstructor: typeof import("@module-federation/runtime-core").getGlobalFederationConstructor;
        setGlobalFederationConstructor: typeof import("@module-federation/runtime-core").setGlobalFederationConstructor;
        getInfoWithoutType: typeof import("@module-federation/runtime-core").getInfoWithoutType;
        getGlobalSnapshot: typeof import("@module-federation/runtime-core").getGlobalSnapshot;
        getTargetSnapshotInfoByModuleInfo: typeof import("packages/runtime-core/dist/src/global").getTargetSnapshotInfoByModuleInfo;
        getGlobalSnapshotInfoByModuleInfo: typeof import("packages/runtime-core/dist/src/global").getGlobalSnapshotInfoByModuleInfo;
        setGlobalSnapshotInfoByModuleInfo: typeof import("packages/runtime-core/dist/src/global").setGlobalSnapshotInfoByModuleInfo;
        addGlobalSnapshot: typeof import("@module-federation/runtime-core").addGlobalSnapshot;
        getRemoteEntryExports: typeof import("packages/runtime-core/dist/src/global").getRemoteEntryExports;
        registerGlobalPlugins: typeof import("@module-federation/runtime-core").registerGlobalPlugins;
        getGlobalHostPlugins: typeof import("packages/runtime-core/dist/src/global").getGlobalHostPlugins;
        getPreloaded: typeof import("packages/runtime-core/dist/src/global").getPreloaded;
        setPreloaded: typeof import("packages/runtime-core/dist/src/global").setPreloaded;
    };
    share: import("@module-federation/runtime-core").IShareUtils;
};
export default _default;
