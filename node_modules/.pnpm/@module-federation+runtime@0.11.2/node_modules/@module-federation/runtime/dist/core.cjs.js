'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var runtimeCore = require('@module-federation/runtime-core');



exports.default = runtimeCore;
Object.keys(runtimeCore).forEach(function (k) {
	if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
		enumerable: true,
		get: function () { return runtimeCore[k]; }
	});
});
