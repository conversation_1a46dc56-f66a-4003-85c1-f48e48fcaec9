{"name": "@module-federation/runtime", "version": "0.11.2", "author": "z<PERSON><PERSON>o <<EMAIL>>", "main": "./dist/index.cjs.js", "module": "./dist/index.esm.mjs", "types": "./dist/index.cjs.d.ts", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/module-federation/core/", "directory": "packages/runtime"}, "files": ["dist/", "README.md"], "exports": {".": {"types": "./dist/index.cjs.d.ts", "import": "./dist/index.esm.mjs", "require": "./dist/index.cjs.js"}, "./helpers": {"types": "./dist/helpers.cjs.d.ts", "import": "./dist/helpers.esm.mjs", "require": "./dist/helpers.cjs.js"}, "./types": {"types": "./dist/types.cjs.d.ts", "import": "./dist/types.esm.mjs", "require": "./dist/types.cjs.js"}, "./core": {"types": "./dist/core.cjs.d.ts", "import": "./dist/core.esm.mjs", "require": "./dist/core.cjs.js"}, "./*": "./*"}, "typesVersions": {"*": {".": ["./dist/index.cjs.d.ts"], "helpers": ["./dist/helpers.cjs.d.ts"], "types": ["./dist/types.cjs.d.ts"], "core": ["./dist/core.cjs.d.ts"]}}, "dependencies": {"@module-federation/sdk": "0.11.2", "@module-federation/runtime-core": "0.11.2", "@module-federation/error-codes": "0.11.2"}}