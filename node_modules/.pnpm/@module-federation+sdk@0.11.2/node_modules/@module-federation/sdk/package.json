{"name": "@module-federation/sdk", "version": "0.11.2", "license": "MIT", "description": "A sdk for support module federation", "keywords": ["Module Federation", "sdk"], "files": ["dist/", "README.md"], "repository": {"type": "git", "url": "https://github.com/module-federation/core/", "directory": "packages/sdk"}, "publishConfig": {"access": "public"}, "author": "z<PERSON><PERSON> <<EMAIL>>", "sideEffects": false, "main": "./dist/index.cjs.js", "module": "./dist/index.esm.mjs", "types": "./dist/index.cjs.d.ts", "exports": {".": {"types": "./dist/index.cjs.d.ts", "import": "./dist/index.esm.mjs", "require": "./dist/index.cjs.js"}, "./normalize-webpack-path": {"types": "./dist/normalize-webpack-path.cjs.d.ts", "import": "./dist/normalize-webpack-path.esm.mjs", "require": "./dist/normalize-webpack-path.cjs.js"}}, "typesVersions": {"*": {".": ["./dist/index.cjs.d.ts"], "normalize-webpack-path": ["./dist/normalize-webpack-path.cjs.d.ts"]}}}