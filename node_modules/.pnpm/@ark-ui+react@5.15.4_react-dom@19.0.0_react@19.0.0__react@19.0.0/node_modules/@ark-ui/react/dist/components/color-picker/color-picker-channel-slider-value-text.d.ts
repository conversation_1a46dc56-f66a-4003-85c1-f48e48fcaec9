import { HTMLProps, PolymorphicProps } from '../factory';
import { ForwardRefExoticComponent, RefAttributes } from 'react';
export interface ColorPickerChannelSliderValueTextBaseProps extends PolymorphicProps {
}
export interface ColorPickerChannelSliderValueTextProps extends HTMLProps<'span'>, ColorPickerChannelSliderValueTextBaseProps {
}
export declare const ColorPickerChannelSliderValueText: ForwardRefExoticComponent<ColorPickerChannelSliderValueTextProps & RefAttributes<HTMLSpanElement>>;
