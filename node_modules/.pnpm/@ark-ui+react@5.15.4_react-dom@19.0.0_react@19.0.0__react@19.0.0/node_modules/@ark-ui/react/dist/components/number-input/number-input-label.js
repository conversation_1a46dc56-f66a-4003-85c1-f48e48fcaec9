'use client';
import { jsx } from 'react/jsx-runtime';
import { mergeProps } from '@zag-js/react';
import { forwardRef } from 'react';
import { ark } from '../factory.js';
import { useNumberInputContext } from './use-number-input-context.js';

const NumberInputLabel = forwardRef((props, ref) => {
  const numberInput = useNumberInputContext();
  const mergedProps = mergeProps(numberInput.getLabelProps(), props);
  return /* @__PURE__ */ jsx(ark.label, { ...mergedProps, ref });
});
NumberInputLabel.displayName = "NumberInputLabel";

export { NumberInputLabel };
