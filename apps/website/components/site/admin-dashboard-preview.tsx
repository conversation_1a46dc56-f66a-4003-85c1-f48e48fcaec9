'use client'

import { Box, Image } from '@chakra-ui/react'
import { useState, useEffect } from 'react'

// 管理后台界面的模拟图片数据
const dashboardImages = [
  {
    src: '/img/admin-dashboard-1.svg',
    alt: '数据概览大屏',
    title: '实时数据监控'
  },
  {
    src: '/img/admin-dashboard-2.svg',
    alt: '智能匹配系统',
    title: 'AI驱动匹配算法'
  },
  {
    src: '/img/admin-dashboard-3.svg',
    alt: '3D数据分析',
    title: '可视化数据大屏'
  },
  {
    src: '/img/admin-dashboard-4.svg',
    alt: '生态平台管理',
    title: '多方共赢生态'
  }
]

interface AdminDashboardPreviewProps {
  variant?: 'default' | 'slideshow' | 'hover'
  autoPlay?: boolean
  interval?: number
}

export function AdminDashboardPreview({ 
  variant = 'slideshow', 
  autoPlay = true,
  interval = 4000 
}: AdminDashboardPreviewProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isHovered, setIsHovered] = useState(false)

  useEffect(() => {
    if (!autoPlay || isHovered) return

    const timer = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % dashboardImages.length)
    }, interval)

    return () => clearInterval(timer)
  }, [autoPlay, interval, isHovered])

  if (variant === 'default') {
    return (
      <Box
        position="relative"
        width="100%"
        height="100%"
        display="flex"
        alignItems="center"
        justifyContent="center"
        bg="gray.50"
        _dark={{ bg: "gray.900" }}
      >
        <Image
          src={dashboardImages[0].src}
          alt={dashboardImages[0].alt}
          width="90%"
          height="auto"
          borderRadius="lg"
          boxShadow="2xl"
          objectFit="contain"
        />
      </Box>
    )
  }

  if (variant === 'hover') {
    return (
      <Box
        position="relative"
        width="100%"
        height="100%"
        display="flex"
        alignItems="center"
        justifyContent="center"
        bg="gray.50"
        _dark={{ bg: "gray.900" }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        cursor="pointer"
      >
        {dashboardImages.map((image, index) => (
          <Image
            key={index}
            src={image.src}
            alt={image.alt}
            position="absolute"
            width="90%"
            height="auto"
            borderRadius="lg"
            boxShadow="2xl"
            objectFit="contain"
            opacity={index === currentIndex ? 1 : 0}
            transition="opacity 0.5s ease-in-out"
            transform={isHovered ? 'scale(1.05)' : 'scale(1)'}
            transitionDuration="0.3s"
          />
        ))}
      </Box>
    )
  }

  // slideshow variant (default)
  return (
    <Box
      position="relative"
      width="100%"
      height="100%"
      display="flex"
      alignItems="center"
      justifyContent="center"
      bg="gray.50"
      _dark={{ bg: "gray.900" }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      overflow="hidden"
    >
      {dashboardImages.map((image, index) => (
        <Box
          key={index}
          position="absolute"
          width="90%"
          height="auto"
          opacity={index === currentIndex ? 1 : 0}
          transition="opacity 0.8s ease-in-out"
          transform={index === currentIndex ? 'scale(1)' : 'scale(0.95)'}
        >
          <Image
            src={image.src}
            alt={image.alt}
            width="100%"
            height="auto"
            borderRadius="lg"
            boxShadow="2xl"
            objectFit="contain"
          />
        </Box>
      ))}
      
      {/* 指示器 */}
      <Box
        position="absolute"
        bottom="4"
        left="50%"
        transform="translateX(-50%)"
        display="flex"
        gap="2"
      >
        {dashboardImages.map((_, index) => (
          <Box
            key={index}
            width="2"
            height="2"
            borderRadius="full"
            bg={index === currentIndex ? "white" : "whiteAlpha.500"}
            cursor="pointer"
            transition="all 0.3s"
            onClick={() => setCurrentIndex(index)}
            _hover={{ bg: "white" }}
          />
        ))}
      </Box>
    </Box>
  )
}
