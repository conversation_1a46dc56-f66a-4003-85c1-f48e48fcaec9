import {
  Badge,
  Box,
  ButtonGroup,
  Container,
  HStack,
  Heading,
  Separator,
  Span,
  Stack,
  Text,
  Image,
} from '@chakra-ui/react'
import { Button, Section } from '@saas-ui/react'
import Link from 'next/link'

import { ActionArrow } from '../action-arrow'
import { ProBadge } from '../pro/pro-badge'
import { AdminDashboardPreview } from './admin-dashboard-preview'

export function ProductsSection() {
  return (
    <Box borderTopWidth="1px" borderStyle="dashed">
      <ProductCard
        title="聚合保险需求流量"
        subtitle="流量聚合"
        badge={
          <Badge
            size="xs"
            variant="outline"
            borderRadius="4px"
            boxShadow="none"
            borderWidth="1.5px"
            color="blue.500"
            borderColor="blue.400"
            fontSize="10px"
            height="4"
            px="0.5"
            fontWeight="semibold"
          >
            微信生态
          </Badge>
        }
        description={[
          '通过微信小程序作为主要流量入口，聚合车险、财险、增值服务等多种保险需求。用户可以便捷地选择服务类型，填写基本信息，一站式解决保险需求。',
          '基于微信生态的天然优势，降低用户获取成本，提升转化效率。为保险行业提供稳定、优质的流量来源。',
        ]}
        primaryAction={{
          label: '查看小程序',
          href: '#miniprogram',
        }}
        secondaryAction={{
          label: '了解更多',
          href: '#features',
        }}
      >
        <AdminDashboardPreview variant="slideshow" />
      </ProductCard>
      <ProductCard
        title="智能匹配优质服务商"
        subtitle="智能分发"
        badge={
          <Badge
            size="xs"
            variant="outline"
            borderRadius="4px"
            boxShadow="none"
            borderWidth="1.5px"
            color="green.500"
            borderColor="green.400"
            fontSize="10px"
            height="4"
            px="0.5"
            fontWeight="semibold"
          >
            AI驱动
          </Badge>
        }
        description={[
          '基于用户地域、需求类型、历史数据等多维度信息，智能推荐最适合的保险公司和服务商。确保用户获得最优质的服务体验。',
          '通过算法优化匹配效率，提升成交转化率。让每一个用户需求都能找到最合适的服务商，实现精准对接。',
        ]}
        primaryAction={{
          label: '查看算法',
          href: '#algorithm',
        }}
        secondaryAction={{
          label: '成功案例',
          href: '#cases',
        }}
      >
        <AdminDashboardPreview variant="hover" />
      </ProductCard>
      <ProductCard
        title="数据驱动优化投放"
        subtitle="数据驱动"
        badge={
          <Badge
            size="xs"
            variant="outline"
            borderRadius="4px"
            boxShadow="none"
            borderWidth="1.5px"
            color="purple.500"
            borderColor="purple.400"
            fontSize="10px"
            height="4"
            px="0.5"
            fontWeight="semibold"
          >
            实时分析
          </Badge>
        }
        description={[
          '全方位数据监控和分析，包括点击率、转化率、用户行为轨迹等关键指标。通过数据洞察持续优化投放策略和用户体验。',
          '3D可视化数据大屏实时展示业务状况，帮助决策者快速了解运营情况，及时调整策略，提升整体投放效果。',
        ]}
        primaryAction={{
          label: '查看数据大屏',
          href: '#dashboard',
        }}
        secondaryAction={{
          label: '数据报告',
          href: '#reports',
        }}
      >
        <AdminDashboardPreview variant="slideshow" interval={3000} />
      </ProductCard>
      <ProductCard
        title="构建多方共赢生态"
        subtitle="多方共赢"
        badge={
          <Badge
            size="xs"
            variant="outline"
            borderRadius="4px"
            boxShadow="none"
            borderWidth="1.5px"
            color="orange.500"
            borderColor="orange.400"
            fontSize="10px"
            height="4"
            px="0.5"
            fontWeight="semibold"
          >
            生态平台
          </Badge>
        }
        description={[
          '连接用户、保险公司、服务商的完整生态链。用户获得便捷服务，保险公司获得优质客源，服务商获得精准订单，平台获得可持续发展。',
          '通过技术手段降低各方成本，提升服务效率，实现真正的多方共赢。为保险行业数字化转型提供完整解决方案。',
        ]}
        primaryAction={{
          label: '加入生态',
          href: '#ecosystem',
        }}
        secondaryAction={{
          label: '合作咨询',
          href: '#contact',
        }}
      >
        <AdminDashboardPreview variant="default" />
      </ProductCard>
    </Box>
  )
}

function ProductCard(props: {
  title: string
  subtitle: string
  badge?: React.ReactNode
  description: string | string[]
  primaryAction: {
    label: string
    href: string
  }
  secondaryAction: {
    label: string
    href: string
  }
  children?: React.ReactNode
}) {
  return (
    <Box as="section" borderBottomWidth="1px" borderStyle="dashed">
      <Container maxW="8xl" overflow="hidden">
        <HStack gap="0">
          <Stack
            flex="1 0 50%"
            width="50%"
            alignItems="flex-start"
            gap="2"
            py="24"
            pe="12"
          >
            <HStack>
              <Text textStyle="md" fontWeight="medium">
                {props.subtitle}
              </Text>
              {props.badge}
            </HStack>
            <Heading as="h3" textStyle="4xl">
              {props.title}
            </Heading>
            {Array.isArray(props.description) ? (
              <>
                {props.description.map((line) => (
                  <Text
                    key={line}
                    textStyle="lg"
                    fontWeight="medium"
                    color="fg.subtle"
                  >
                    {line}
                  </Text>
                ))}
              </>
            ) : (
              <Text textStyle="lg" fontWeight="medium" color="fg.subtle">
                {props.description}
              </Text>
            )}
            <ButtonGroup>
              <Button
                variant="plain"
                colorPalette="accent"
                width="auto"
                size="xl"
                asChild
              >
                <Link href={props.primaryAction.href}>
                  {props.primaryAction.label} <ActionArrow />
                </Link>
              </Button>
            </ButtonGroup>
          </Stack>
          <Box
            alignSelf="stretch"
            flexShrink="0"
            minH="540px"
            width="calc(50vw - env(safe-area-inset-right))"
            borderLeftWidth="1px"
            borderStyle="dashed"
          >
            {props.children}
          </Box>
        </HStack>
      </Container>
    </Box>
  )
}
