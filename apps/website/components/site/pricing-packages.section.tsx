'use client'

import {
  Box,
  Container,
  Heading,
  Text,
  SimpleGrid,
  VStack,
  HStack,
  Button,
  Badge,

  Stack
} from '@chakra-ui/react'


// 套餐数据
const packages = [
  {
    name: '微信小程序',
    subtitle: '流量聚合入口',
    price: '¥29,800',
    period: '一次性',
    badge: '热门',
    badgeColor: 'blue',
    features: [
      '微信小程序开发',
      '多服务商选择',
      '智能地域匹配',
      '一键预约功能',
      '实时状态跟踪',
      '用户体验优化',
      '基础数据统计',
      '1年技术支持'
    ],
    buttonText: '立即咨询',
    buttonVariant: 'outline'
  },
  {
    name: '管理后台',
    subtitle: '业务运营中心',
    price: '¥39,800',
    period: '一次性',
    badge: '推荐',
    badgeColor: 'green',
    features: [
      'Web管理后台',
      '多租户管理',
      '预约流程管理',
      '服务商配置',
      '数据统计分析',
      '实时监控预警',
      '客服对话管理',
      '权限角色管理',
      '1年技术支持'
    ],
    buttonText: '立即咨询',
    buttonVariant: 'outline'
  },
  {
    name: '数据大屏',
    subtitle: '可视化分析',
    price: '¥49,800',
    period: '一次性',
    badge: '专业',
    badgeColor: 'purple',
    features: [
      '3D数据大屏',
      '实时数据展示',
      '地域热力分布',
      '多维度统计',
      '趋势分析图表',
      '自定义指标',
      '大屏投影支持',
      '1年技术支持'
    ],
    buttonText: '立即咨询',
    buttonVariant: 'outline'
  },
  {
    name: '全套解决方案',
    subtitle: '完整生态平台',
    price: '¥99,800',
    period: '一次性',
    originalPrice: '¥119,400',
    badge: '最划算',
    badgeColor: 'orange',
    features: [
      '微信小程序 + 管理后台 + 数据大屏',
      '完整业务流程闭环',
      '定制化开发服务',
      'AI智能匹配算法',
      '高级数据分析',
      '专属客服支持',
      '系统集成对接',
      '培训和部署服务',
      '2年技术支持',
      '免费功能升级'
    ],
    buttonText: '立即购买',
    buttonVariant: 'solid',
    isPopular: true
  }
]

export function PricingPackagesSection() {
  return (
    <Box py="20" bg="white" _dark={{ bg: "gray.800" }}>
      <Container maxW="8xl">
        {/* 标题区域 */}
        <VStack gap="4" mb="16" textAlign="center">
          <Badge size="lg" colorPalette="blue" variant="subtle">
            产品套餐
          </Badge>
          <Heading size="2xl">选择适合您的解决方案</Heading>
          <Text 
            fontSize="lg" 
            color="gray.600" 
            _dark={{ color: "gray.400" }}
            maxW="2xl"
          >
            从单一产品到完整生态，我们提供灵活的套餐选择，满足不同规模企业的需求
          </Text>
        </VStack>

        {/* 套餐卡片 */}
        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} gap="6">
          {packages.map((pkg, index) => (
            <Box
              key={index}
              position="relative"
              bg={pkg.isPopular ? "blue.50" : "gray.50"}
              _dark={{ 
                bg: pkg.isPopular ? "blue.900" : "gray.700" 
              }}
              borderRadius="xl"
              p="6"
              border="2px"
              borderColor={pkg.isPopular ? "blue.200" : "transparent"}
              _dark={{
                borderColor: pkg.isPopular ? "blue.600" : "transparent"
              }}
              transition="all 0.3s"
              _hover={{
                transform: "translateY(-4px)",
                boxShadow: "xl"
              }}
            >
              {/* 推荐标签 */}
              {pkg.badge && (
                <Badge
                  position="absolute"
                  top="-3"
                  left="50%"
                  transform="translateX(-50%)"
                  colorPalette={pkg.badgeColor}
                  size="sm"
                  px="3"
                  py="1"
                >
                  {pkg.badge}
                </Badge>
              )}

              <VStack gap="6" align="stretch">
                {/* 套餐信息 */}
                <VStack gap="2" textAlign="center">
                  <Heading size="lg">{pkg.name}</Heading>
                  <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>
                    {pkg.subtitle}
                  </Text>
                  <HStack justify="center" align="baseline">
                    <Heading size="xl" color="blue.500">
                      {pkg.price}
                    </Heading>
                    {pkg.originalPrice && (
                      <Text 
                        fontSize="lg" 
                        textDecoration="line-through" 
                        color="gray.500"
                      >
                        {pkg.originalPrice}
                      </Text>
                    )}
                  </HStack>
                  <Text fontSize="sm" color="gray.500">
                    {pkg.period}
                  </Text>
                </VStack>

                {/* 功能列表 */}
                <VStack spacing="2" align="stretch">
                  {pkg.features.map((feature, featureIndex) => (
                    <HStack key={featureIndex} fontSize="sm">
                      <Text color="green.500" fontSize="sm">✓</Text>
                      <Text>{feature}</Text>
                    </HStack>
                  ))}
                </VStack>

                {/* 购买按钮 */}
                <Button
                  size="lg"
                  variant={pkg.buttonVariant}
                  colorPalette={pkg.isPopular ? "blue" : "gray"}
                  width="full"
                >
                  {pkg.buttonText}
                </Button>
              </VStack>
            </Box>
          ))}
        </SimpleGrid>

        {/* 底部说明 */}
        <VStack gap="4" mt="16" textAlign="center">
          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>
            所有套餐均包含源代码交付、部署指导和技术文档
          </Text>
          <Text fontSize="sm" color="gray.500" _dark={{ color: "gray.500" }}>
            企业定制需求请联系我们获取专属报价方案
          </Text>
        </VStack>
      </Container>
    </Box>
  )
}
