'use client'

import { ActionArrow } from '@/components/action-arrow'
import { Annoucement } from '@/components/annoucement'
import { Subheading } from '@/components/site/typography'
import {
  Box,
  Button,
  Container,
  Heading,
  Stack,
  SimpleGrid,
  Text,
  Badge,
  HStack,
  VStack
} from '@chakra-ui/react'
import Link from 'next/link'

// 核心价值卡片数据
const coreValues = [
  {
    icon: '🔗',
    title: '流量聚合',
    description: '微信小程序聚合保险需求，降低获客成本',
    color: 'blue'
  },
  {
    icon: '🤖',
    title: '智能分发',
    description: 'AI算法精准匹配优质服务商，提升转化率',
    color: 'green'
  },
  {
    icon: '📊',
    title: '数据驱动',
    description: '实时监控分析，持续优化投放效果',
    color: 'purple'
  },
  {
    icon: '🤝',
    title: '多方共赢',
    description: '构建完整生态链，实现各方价值最大化',
    color: 'orange'
  }
]

export const HeroSection = () => {
  return (
    <Box px="8" bg="gray.50" _dark={{ bg: "gray.900" }}>
      <Box pt="32" pb="20" overflow="hidden" position="relative">
        <Container maxW="8xl" zIndex="1" height="100%">
          {/* 主标题区域 */}
          <Stack
            gap={{ base: '5', md: '8' }}
            mb="16"
            alignItems="center"
            textAlign="center"
          >
            <Annoucement justifySelf="center" asChild>
              <Link href="#products">
                保险流量投放生态平台
                <ActionArrow />
              </Link>
            </Annoucement>

            <Stack gap="5" maxW="4xl" alignItems="center">
              <Heading as="h1" fontSize="6xl" lineHeight="1.1" textWrap="balance">
                连接用户需求与服务商
                <br />
                <Text as="span" color="blue.500">数据驱动精准投放</Text>
              </Heading>
              <Subheading maxW="3xl">
                通过微信小程序聚合保险需求流量，智能分发匹配优质服务商，
                数据驱动优化投放效果，打造多方共赢的保险服务生态平台
              </Subheading>
            </Stack>

            <Stack direction={{ base: 'column', sm: 'row' }} gap="3">
              <Button
                size="xl"
                minW="200px"
                asChild
                variant="solid"
                colorPalette="blue"
              >
                <Link href="#products">立即咨询</Link>
              </Button>
              <Button
                size="xl"
                minW="200px"
                asChild
                variant="outline"
                colorPalette="blue"
              >
                <Link href="#products">查看方案</Link>
              </Button>
            </Stack>
          </Stack>

          {/* 核心价值卡片 */}
          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} gap="6" mt="16">
            {coreValues.map((value, index) => (
              <Box
                key={index}
                bg="white"
                _dark={{ bg: "gray.800" }}
                p="6"
                borderRadius="xl"
                boxShadow="lg"
                textAlign="center"
                transition="all 0.3s"
                _hover={{
                  transform: "translateY(-4px)",
                  boxShadow: "xl"
                }}
              >
                <VStack gap="4">
                  <Text fontSize="3xl">{value.icon}</Text>
                  <VStack gap="2">
                    <HStack>
                      <Heading size="md">{value.title}</Heading>
                      <Badge
                        size="sm"
                        colorPalette={value.color}
                        variant="subtle"
                      >
                        核心
                      </Badge>
                    </HStack>
                    <Text
                      fontSize="sm"
                      color="gray.600"
                      _dark={{ color: "gray.400" }}
                      lineHeight="1.6"
                    >
                      {value.description}
                    </Text>
                  </VStack>
                </VStack>
              </Box>
            ))}
          </SimpleGrid>
        </Container>
      </Box>
    </Box>
  )
}
