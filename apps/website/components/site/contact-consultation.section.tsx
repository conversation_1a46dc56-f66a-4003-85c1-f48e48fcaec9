'use client'

import {
  Box,
  Container,
  Heading,
  Text,
  SimpleGrid,
  VStack,
  HStack,
  Button,
  Badge,
  Image,
  Stack
} from '@chakra-ui/react'

export function ContactConsultationSection() {
  return (
    <Box py="20" bg="blue.50" _dark={{ bg: "blue.900" }}>
      <Container maxW="6xl">
        <VStack gap="12" textAlign="center">
          {/* 标题区域 */}
          <VStack gap="4">
            <Badge size="lg" colorPalette="blue" variant="solid">
              立即行动
            </Badge>
            <Heading size="2xl" color="blue.900" _dark={{ color: "blue.100" }}>
              开启您的保险流量投放之旅
            </Heading>
            <Text 
              fontSize="lg" 
              color="blue.700" 
              _dark={{ color: "blue.300" }}
              maxW="2xl"
            >
              扫码添加微信，获取详细产品方案和专属报价
            </Text>
          </VStack>

          {/* 联系方式 */}
          <SimpleGrid columns={{ base: 1, md: 2 }} gap="8" w="full" maxW="4xl">
            {/* 微信咨询 */}
            <Box
              bg="white"
              _dark={{ bg: "gray.800" }}
              p="8"
              borderRadius="xl"
              boxShadow="lg"
              textAlign="center"
              transition="all 0.3s"
              _hover={{
                transform: "translateY(-4px)",
                boxShadow: "xl"
              }}
            >
              <VStack gap="4">
                <Text fontSize="xl" fontWeight="bold">
                  微信咨询
                </Text>
                <Box
                  width="200px"
                  height="200px"
                  bg="gray.100"
                  _dark={{ bg: "gray.700" }}
                  borderRadius="lg"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  border="2px dashed"
                  borderColor="gray.300"
                  _dark={{ borderColor: "gray.600" }}
                >
                  <VStack gap="2">
                    <Text fontSize="4xl">📱</Text>
                    <Text fontSize="sm" color="gray.500">
                      微信二维码位置
                    </Text>
                    <Text fontSize="xs" color="gray.400">
                      扫码添加微信
                    </Text>
                  </VStack>
                </Box>
                <VStack gap="2">
                  <Text fontSize="sm" fontWeight="medium">
                    专业顾问一对一服务
                  </Text>
                  <Text fontSize="xs" color="gray.600" _dark={{ color: "gray.400" }}>
                    工作时间：9:00-18:00
                  </Text>
                </VStack>
              </VStack>
            </Box>

            {/* 在线客服 */}
            <Box
              bg="white"
              _dark={{ bg: "gray.800" }}
              p="8"
              borderRadius="xl"
              boxShadow="lg"
              textAlign="center"
              transition="all 0.3s"
              _hover={{
                transform: "translateY(-4px)",
                boxShadow: "xl"
              }}
            >
              <VStack gap="4">
                <Text fontSize="xl" fontWeight="bold">
                  在线客服
                </Text>
                <Box
                  width="200px"
                  height="200px"
                  bg="gray.100"
                  _dark={{ bg: "gray.700" }}
                  borderRadius="lg"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  border="2px dashed"
                  borderColor="gray.300"
                  _dark={{ borderColor: "gray.600" }}
                >
                  <VStack gap="2">
                    <Text fontSize="4xl">💬</Text>
                    <Text fontSize="sm" color="gray.500">
                      客服二维码位置
                    </Text>
                    <Text fontSize="xs" color="gray.400">
                      扫码在线咨询
                    </Text>
                  </VStack>
                </Box>
                <VStack gap="2">
                  <Text fontSize="sm" fontWeight="medium">
                    7×24小时在线支持
                  </Text>
                  <Text fontSize="xs" color="gray.600" _dark={{ color: "gray.400" }}>
                    快速响应，专业解答
                  </Text>
                </VStack>
              </VStack>
            </Box>
          </SimpleGrid>

          {/* 服务承诺 */}
          <Box
            bg="white"
            _dark={{ bg: "gray.800" }}
            p="6"
            borderRadius="xl"
            boxShadow="md"
            w="full"
            maxW="4xl"
          >
            <VStack gap="4">
              <Heading size="md">我们的服务承诺</Heading>
              <SimpleGrid columns={{ base: 1, md: 3 }} gap="6" w="full">
                <VStack gap="2">
                  <Text fontSize="2xl">⚡</Text>
                  <Text fontSize="sm" fontWeight="medium">快速响应</Text>
                  <Text fontSize="xs" color="gray.600" _dark={{ color: "gray.400" }} textAlign="center">
                    1小时内回复咨询
                  </Text>
                </VStack>
                <VStack gap="2">
                  <Text fontSize="2xl">🎯</Text>
                  <Text fontSize="sm" fontWeight="medium">专业方案</Text>
                  <Text fontSize="xs" color="gray.600" _dark={{ color: "gray.400" }} textAlign="center">
                    定制化解决方案
                  </Text>
                </VStack>
                <VStack gap="2">
                  <Text fontSize="2xl">🛡️</Text>
                  <Text fontSize="sm" fontWeight="medium">售后保障</Text>
                  <Text fontSize="xs" color="gray.600" _dark={{ color: "gray.400" }} textAlign="center">
                    长期技术支持
                  </Text>
                </VStack>
              </SimpleGrid>
            </VStack>
          </Box>

          {/* 立即行动按钮 */}
          <Stack direction={{ base: 'column', sm: 'row' }} gap="4">
            <Button
              size="xl"
              colorPalette="blue"
              variant="solid"
              minW="200px"
            >
              立即咨询方案
            </Button>
            <Button
              size="xl"
              colorPalette="blue"
              variant="outline"
              minW="200px"
            >
              预约演示
            </Button>
          </Stack>
        </VStack>
      </Container>
    </Box>
  )
}
