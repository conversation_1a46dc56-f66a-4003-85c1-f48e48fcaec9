{"Skeleton": {"props": {"colorPalette": {"defaultValue": "gray", "type": "\"gray\" | \"zinc\" | \"neutral\" | \"stone\" | \"red\" | \"orange\" | \"amber\" | \"yellow\" | \"lime\" | \"green\" | \"emerald\" | \"teal\" | \"cyan\" | \"sky\" | \"blue\" | \"indigo\" | \"violet\" | \"purple\" | \"fuchsia\" | \"pink\" | \"rose\" | \"presence\" | \"status\" | \"sidebar\" | \"sidebar.accent\" | \"accent\" | \"slate\"", "isRequired": false, "description": "The color palette of the component"}, "loading": {"type": "\"true\" | \"false\"", "defaultValue": true, "isRequired": false, "description": "The loading of the component"}, "variant": {"type": "\"pulse\" | \"shine\" | \"none\"", "defaultValue": "pulse", "isRequired": false, "description": "The variant of the component"}}}}