{"Root": {"props": {"colorPalette": {"defaultValue": "gray", "type": "\"gray\" | \"zinc\" | \"neutral\" | \"stone\" | \"red\" | \"orange\" | \"amber\" | \"yellow\" | \"lime\" | \"green\" | \"emerald\" | \"teal\" | \"cyan\" | \"sky\" | \"blue\" | \"indigo\" | \"violet\" | \"purple\" | \"fuchsia\" | \"pink\" | \"rose\" | \"presence\" | \"status\" | \"sidebar\" | \"sidebar.accent\" | \"accent\" | \"slate\"", "isRequired": false, "description": "The color palette of the component"}, "size": {"type": "\"sm\" | \"md\" | \"lg\" | \"xl\"", "defaultValue": "md", "isRequired": false, "description": "The size of the component"}, "variant": {"type": "\"subtle\" | \"solid\" | \"outline\" | \"surface\"", "defaultValue": "surface", "isRequired": false, "description": "The variant of the component"}}}}