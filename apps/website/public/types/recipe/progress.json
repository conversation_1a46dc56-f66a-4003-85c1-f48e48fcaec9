{"Root": {"props": {"colorPalette": {"defaultValue": "gray", "type": "\"gray\" | \"zinc\" | \"neutral\" | \"stone\" | \"red\" | \"orange\" | \"amber\" | \"yellow\" | \"lime\" | \"green\" | \"emerald\" | \"teal\" | \"cyan\" | \"sky\" | \"blue\" | \"indigo\" | \"violet\" | \"purple\" | \"fuchsia\" | \"pink\" | \"rose\" | \"presence\" | \"status\" | \"sidebar\" | \"sidebar.accent\" | \"accent\" | \"slate\"", "isRequired": false, "description": "The color palette of the component"}, "variant": {"type": "\"outline\" | \"subtle\"", "defaultValue": "outline", "isRequired": false, "description": "The variant of the component"}, "shape": {"type": "\"square\" | \"rounded\" | \"full\"", "defaultValue": "rounded", "isRequired": false, "description": "The shape of the component"}, "striped": {"type": "\"true\" | \"false\"", "isRequired": false, "description": "The striped of the component"}, "animated": {"type": "\"true\" | \"false\"", "isRequired": false, "description": "The animated of the component"}, "size": {"type": "\"xs\" | \"sm\" | \"md\" | \"lg\" | \"xl\"", "defaultValue": "md", "isRequired": false, "description": "The size of the component"}}}}