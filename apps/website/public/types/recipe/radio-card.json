{"Root": {"props": {"colorPalette": {"defaultValue": "gray", "type": "\"gray\" | \"zinc\" | \"neutral\" | \"stone\" | \"red\" | \"orange\" | \"amber\" | \"yellow\" | \"lime\" | \"green\" | \"emerald\" | \"teal\" | \"cyan\" | \"sky\" | \"blue\" | \"indigo\" | \"violet\" | \"purple\" | \"fuchsia\" | \"pink\" | \"rose\" | \"presence\" | \"status\" | \"sidebar\" | \"sidebar.accent\" | \"accent\" | \"slate\"", "isRequired": false, "description": "The color palette of the component"}, "size": {"type": "\"sm\" | \"md\" | \"lg\"", "defaultValue": "md", "isRequired": false, "description": "The size of the component"}, "variant": {"type": "\"surface\" | \"subtle\" | \"outline\" | \"solid\"", "defaultValue": "outline", "isRequired": false, "description": "The variant of the component"}, "justify": {"type": "\"start\" | \"end\" | \"center\"", "isRequired": false, "description": "The justify of the component"}, "align": {"type": "\"start\" | \"end\" | \"center\"", "defaultValue": "start", "isRequired": false, "description": "The align of the component"}, "orientation": {"type": "\"vertical\" | \"horizontal\"", "defaultValue": "horizontal", "isRequired": false, "description": "The orientation of the component"}}}}