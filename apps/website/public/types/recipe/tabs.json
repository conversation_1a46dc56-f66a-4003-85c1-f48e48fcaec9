{"Root": {"props": {"colorPalette": {"defaultValue": "gray", "type": "\"gray\" | \"zinc\" | \"neutral\" | \"stone\" | \"red\" | \"orange\" | \"amber\" | \"yellow\" | \"lime\" | \"green\" | \"emerald\" | \"teal\" | \"cyan\" | \"sky\" | \"blue\" | \"indigo\" | \"violet\" | \"purple\" | \"fuchsia\" | \"pink\" | \"rose\" | \"presence\" | \"status\" | \"sidebar\" | \"sidebar.accent\" | \"accent\" | \"slate\"", "isRequired": false, "description": "The color palette of the component"}, "fitted": {"type": "\"true\" | \"false\"", "isRequired": false, "description": "The fitted of the component"}, "justify": {"type": "\"start\" | \"center\" | \"end\"", "isRequired": false, "description": "The justify of the component"}, "size": {"type": "\"xs\" | \"sm\" | \"md\" | \"lg\"", "defaultValue": "md", "isRequired": false, "description": "The size of the component"}, "variant": {"type": "\"line\" | \"pills\" | \"ghost\" | \"enclosed\" | \"outline\" | \"plain\"", "defaultValue": "line", "isRequired": false, "description": "The variant of the component"}}}}