{"Root": {"props": {"colorPalette": {"defaultValue": "gray", "type": "\"gray\" | \"zinc\" | \"neutral\" | \"stone\" | \"red\" | \"orange\" | \"amber\" | \"yellow\" | \"lime\" | \"green\" | \"emerald\" | \"teal\" | \"cyan\" | \"sky\" | \"blue\" | \"indigo\" | \"violet\" | \"purple\" | \"fuchsia\" | \"pink\" | \"rose\" | \"presence\" | \"status\" | \"sidebar\" | \"sidebar.accent\" | \"accent\" | \"slate\"", "isRequired": false, "description": "The color palette of the component"}, "variant": {"type": "\"marker\" | \"plain\"", "defaultValue": "marker", "isRequired": false, "description": "The variant of the component"}, "align": {"type": "\"center\" | \"start\" | \"end\"", "isRequired": false, "description": "The align of the component"}}}}