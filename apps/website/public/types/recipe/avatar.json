{"Root": {"props": {"colorPalette": {"defaultValue": "gray", "type": "\"gray\" | \"zinc\" | \"neutral\" | \"stone\" | \"red\" | \"orange\" | \"amber\" | \"yellow\" | \"lime\" | \"green\" | \"emerald\" | \"teal\" | \"cyan\" | \"sky\" | \"blue\" | \"indigo\" | \"violet\" | \"purple\" | \"fuchsia\" | \"pink\" | \"rose\" | \"presence\" | \"status\" | \"sidebar\" | \"sidebar.accent\" | \"accent\" | \"slate\"", "isRequired": false, "description": "The color palette of the component"}, "size": {"type": "\"full\" | \"2xs\" | \"xs\" | \"sm\" | \"md\" | \"lg\" | \"xl\" | \"2xl\"", "defaultValue": "md", "isRequired": false, "description": "The size of the component"}, "variant": {"type": "\"solid\" | \"subtle\" | \"outline\"", "defaultValue": "solid", "isRequired": false, "description": "The variant of the component"}, "shape": {"type": "\"square\" | \"rounded\" | \"full\"", "defaultValue": "full", "isRequired": false, "description": "The shape of the component"}, "borderless": {"type": "\"true\" | \"false\"", "isRequired": false, "description": "The borderless of the component"}}}}