{"Root": {"props": {"colorPalette": {"defaultValue": "gray", "type": "\"gray\" | \"zinc\" | \"neutral\" | \"stone\" | \"red\" | \"orange\" | \"amber\" | \"yellow\" | \"lime\" | \"green\" | \"emerald\" | \"teal\" | \"cyan\" | \"sky\" | \"blue\" | \"indigo\" | \"violet\" | \"purple\" | \"fuchsia\" | \"pink\" | \"rose\" | \"presence\" | \"status\" | \"sidebar\" | \"sidebar.accent\" | \"accent\" | \"slate\"", "isRequired": false, "description": "The color palette of the component"}, "size": {"type": "\"xs\" | \"sm\" | \"md\" | \"lg\" | \"xl\" | \"full\"", "defaultValue": "xs", "isRequired": false, "description": "The size of the component"}, "placement": {"type": "\"start\" | \"end\" | \"top\" | \"bottom\"", "defaultValue": "end", "isRequired": false, "description": "The placement of the component"}, "attached": {"type": "\"true\" | \"false\"", "isRequired": false, "description": "The attached of the component"}}}}