{"PasswordInput": {"props": {"defaultVisible": {"type": "boolean", "description": "The default visibility state of the password input.", "defaultValue": "false"}, "visible": {"type": "boolean", "description": "The controlled visibility state of the password input."}, "onVisibleChange": {"type": "(visible: boolean) => void", "description": "Callback invoked when the visibility state changes."}, "visibilityIcon": {"type": "{ on: React.ReactNode; off: React.ReactNode }", "description": "Custom icons for the visibility toggle button."}}}}