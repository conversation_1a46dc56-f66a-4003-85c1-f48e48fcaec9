{"EmptyState": {"props": {"title": {"type": "string", "isRequired": true}, "description": {"type": "string", "isRequired": false}, "icon": {"type": "React.ReactNode", "isRequired": false}}}, "Root": {"props": {"as": {"type": "React.ElementType", "isRequired": false, "description": "The underlying element to render."}, "asChild": {"type": "boolean", "isRequired": false, "description": "Use the provided child element as the default rendered element, combining their props and behavior."}, "unstyled": {"type": "boolean", "isRequired": false, "description": "Whether to remove the component's style."}}}}