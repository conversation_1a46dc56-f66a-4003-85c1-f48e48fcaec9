{"FileUploadItem": {"props": {"file": {"type": "File", "isRequired": true}, "showSize": {"type": "boolean", "isRequired": false}, "showDelete": {"type": "boolean", "isRequired": false}}}, "FileUploadRoot": {"props": {"inputProps": {"type": "React.InputHTMLAttributes<HTMLInputElement>", "isRequired": false}}}, "Root": {"props": {"as": {"type": "React.ElementType", "isRequired": false, "description": "The underlying element to render."}, "asChild": {"type": "boolean", "isRequired": false, "description": "Use the provided child element as the default rendered element, combining their props and behavior."}, "unstyled": {"type": "boolean", "isRequired": false, "description": "Whether to remove the component's style."}}}}