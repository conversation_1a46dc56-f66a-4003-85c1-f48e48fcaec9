<svg width="800" height="600" viewBox="0 0 800 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="800" height="600" fill="#f8fafc"/>
  
  <!-- 顶部导航栏 -->
  <rect width="800" height="60" fill="#ffffff"/>
  <rect x="0" y="60" width="800" height="1" fill="#e2e8f0"/>
  
  <!-- Logo区域 -->
  <rect x="20" y="15" width="120" height="30" rx="4" fill="#3b82f6"/>
  <text x="80" y="35" text-anchor="middle" fill="white" font-family="Arial" font-size="14" font-weight="bold">保险流量平台</text>
  
  <!-- 用户信息 -->
  <circle cx="740" cy="30" r="15" fill="#e2e8f0"/>
  <text x="740" y="35" text-anchor="middle" fill="#64748b" font-family="Arial" font-size="12">管理员</text>
  
  <!-- 侧边栏 -->
  <rect x="0" y="60" width="200" height="540" fill="#ffffff"/>
  <rect x="200" y="60" width="1" height="540" fill="#e2e8f0"/>
  
  <!-- 侧边栏菜单项 -->
  <rect x="10" y="80" width="180" height="40" rx="6" fill="#3b82f6" fill-opacity="0.1"/>
  <text x="20" y="105" fill="#3b82f6" font-family="Arial" font-size="14" font-weight="500">📊 数据概览</text>
  
  <text x="20" y="145" fill="#64748b" font-family="Arial" font-size="14">👥 用户管理</text>
  <text x="20" y="175" fill="#64748b" font-family="Arial" font-size="14">📋 订单管理</text>
  <text x="20" y="205" fill="#64748b" font-family="Arial" font-size="14">🏢 服务商管理</text>
  <text x="20" y="235" fill="#64748b" font-family="Arial" font-size="14">📈 数据分析</text>
  <text x="20" y="265" fill="#64748b" font-family="Arial" font-size="14">⚙️ 系统设置</text>
  
  <!-- 主内容区域 -->
  <rect x="220" y="80" width="560" height="500" fill="#ffffff"/>
  
  <!-- 页面标题 -->
  <text x="240" y="110" fill="#1e293b" font-family="Arial" font-size="24" font-weight="bold">数据概览</text>
  <text x="240" y="135" fill="#64748b" font-family="Arial" font-size="14">实时监控平台运营数据</text>
  
  <!-- 统计卡片 -->
  <rect x="240" y="160" width="120" height="80" rx="8" fill="#ffffff" stroke="#e2e8f0"/>
  <text x="250" y="180" fill="#64748b" font-family="Arial" font-size="12">今日访问量</text>
  <text x="250" y="205" fill="#1e293b" font-family="Arial" font-size="24" font-weight="bold">12,345</text>
  <text x="250" y="225" fill="#10b981" font-family="Arial" font-size="12">↗ +12.5%</text>
  
  <rect x="380" y="160" width="120" height="80" rx="8" fill="#ffffff" stroke="#e2e8f0"/>
  <text x="390" y="180" fill="#64748b" font-family="Arial" font-size="12">转化订单</text>
  <text x="390" y="205" fill="#1e293b" font-family="Arial" font-size="24" font-weight="bold">1,234</text>
  <text x="390" y="225" fill="#10b981" font-family="Arial" font-size="12">↗ +8.3%</text>
  
  <rect x="520" y="160" width="120" height="80" rx="8" fill="#ffffff" stroke="#e2e8f0"/>
  <text x="530" y="180" fill="#64748b" font-family="Arial" font-size="12">活跃用户</text>
  <text x="530" y="205" fill="#1e293b" font-family="Arial" font-size="24" font-weight="bold">8,567</text>
  <text x="530" y="225" fill="#10b981" font-family="Arial" font-size="12">↗ +15.2%</text>
  
  <rect x="660" y="160" width="120" height="80" rx="8" fill="#ffffff" stroke="#e2e8f0"/>
  <text x="670" y="180" fill="#64748b" font-family="Arial" font-size="12">收入金额</text>
  <text x="670" y="205" fill="#1e293b" font-family="Arial" font-size="24" font-weight="bold">¥89.2K</text>
  <text x="670" y="225" fill="#10b981" font-family="Arial" font-size="12">↗ +22.1%</text>
  
  <!-- 图表区域 -->
  <rect x="240" y="270" width="540" height="280" rx="8" fill="#ffffff" stroke="#e2e8f0"/>
  <text x="250" y="295" fill="#1e293b" font-family="Arial" font-size="16" font-weight="600">流量趋势图</text>
  
  <!-- 模拟折线图 -->
  <polyline points="260,450 300,420 340,440 380,400 420,380 460,360 500,340 540,320 580,300 620,280 660,260 700,240 740,220" 
            stroke="#3b82f6" stroke-width="3" fill="none"/>
  
  <!-- 图表网格线 -->
  <line x1="260" y1="320" x2="760" y2="320" stroke="#e2e8f0" stroke-width="1"/>
  <line x1="260" y1="360" x2="760" y2="360" stroke="#e2e8f0" stroke-width="1"/>
  <line x1="260" y1="400" x2="760" y2="400" stroke="#e2e8f0" stroke-width="1"/>
  <line x1="260" y1="440" x2="760" y2="440" stroke="#e2e8f0" stroke-width="1"/>
  
  <!-- 图表数据点 -->
  <circle cx="300" cy="420" r="4" fill="#3b82f6"/>
  <circle cx="380" cy="400" r="4" fill="#3b82f6"/>
  <circle cx="460" cy="360" r="4" fill="#3b82f6"/>
  <circle cx="540" cy="320" r="4" fill="#3b82f6"/>
  <circle cx="620" cy="280" r="4" fill="#3b82f6"/>
  <circle cx="700" cy="240" r="4" fill="#3b82f6"/>
</svg>
