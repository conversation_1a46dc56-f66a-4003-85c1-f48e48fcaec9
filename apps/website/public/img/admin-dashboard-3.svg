<svg width="800" height="600" viewBox="0 0 800 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="800" height="600" fill="#fefefe"/>
  
  <!-- 顶部导航栏 -->
  <rect width="800" height="60" fill="#ffffff"/>
  <rect x="0" y="60" width="800" height="1" fill="#e5e7eb"/>
  
  <!-- Logo区域 -->
  <rect x="20" y="15" width="140" height="30" rx="4" fill="#7c3aed"/>
  <text x="90" y="35" text-anchor="middle" fill="white" font-family="Arial" font-size="14" font-weight="bold">数据分析大屏</text>
  
  <!-- 用户信息 -->
  <circle cx="740" cy="30" r="15" fill="#e5e7eb"/>
  <text x="740" y="35" text-anchor="middle" fill="#6b7280" font-family="Arial" font-size="12">数据员</text>
  
  <!-- 侧边栏 -->
  <rect x="0" y="60" width="200" height="540" fill="#ffffff"/>
  <rect x="200" y="60" width="1" height="540" fill="#e5e7eb"/>
  
  <!-- 侧边栏菜单项 -->
  <rect x="10" y="140" width="180" height="40" rx="6" fill="#7c3aed" fill-opacity="0.1"/>
  <text x="20" y="165" fill="#7c3aed" font-family="Arial" font-size="14" font-weight="500">📊 数据大屏</text>
  
  <text x="20" y="195" fill="#6b7280" font-family="Arial" font-size="14">📈 投放分析</text>
  <text x="20" y="225" fill="#6b7280" font-family="Arial" font-size="14">🎯 转化漏斗</text>
  <text x="20" y="255" fill="#6b7280" font-family="Arial" font-size="14">👥 用户画像</text>
  <text x="20" y="285" fill="#6b7280" font-family="Arial" font-size="14">📋 报表中心</text>
  
  <!-- 主内容区域 -->
  <rect x="220" y="80" width="560" height="500" fill="#f9fafb"/>
  
  <!-- 页面标题 -->
  <text x="240" y="110" fill="#111827" font-family="Arial" font-size="24" font-weight="bold">实时数据大屏</text>
  <text x="240" y="135" fill="#6b7280" font-family="Arial" font-size="14">3D可视化展示平台运营数据</text>
  
  <!-- 核心指标区域 -->
  <rect x="240" y="150" width="520" height="120" rx="8" fill="#ffffff" stroke="#e5e7eb"/>
  <text x="250" y="175" fill="#111827" font-family="Arial" font-size="16" font-weight="600">核心业务指标</text>
  
  <!-- 3D效果的数据卡片 -->
  <g transform="translate(260, 190)">
    <rect width="100" height="60" rx="6" fill="#3b82f6" transform="skewY(-5deg)"/>
    <rect x="2" y="-2" width="100" height="60" rx="6" fill="#60a5fa"/>
    <text x="52" y="20" text-anchor="middle" fill="white" font-family="Arial" font-size="12">日活用户</text>
    <text x="52" y="40" text-anchor="middle" fill="white" font-family="Arial" font-size="18" font-weight="bold">15.2K</text>
  </g>
  
  <g transform="translate(380, 190)">
    <rect width="100" height="60" rx="6" fill="#10b981" transform="skewY(-5deg)"/>
    <rect x="2" y="-2" width="100" height="60" rx="6" fill="#34d399"/>
    <text x="52" y="20" text-anchor="middle" fill="white" font-family="Arial" font-size="12">转化率</text>
    <text x="52" y="40" text-anchor="middle" fill="white" font-family="Arial" font-size="18" font-weight="bold">8.7%</text>
  </g>
  
  <g transform="translate(500, 190)">
    <rect width="100" height="60" rx="6" fill="#f59e0b" transform="skewY(-5deg)"/>
    <rect x="2" y="-2" width="100" height="60" rx="6" fill="#fbbf24"/>
    <text x="52" y="20" text-anchor="middle" fill="white" font-family="Arial" font-size="12">收入</text>
    <text x="52" y="40" text-anchor="middle" fill="white" font-family="Arial" font-size="18" font-weight="bold">¥126K</text>
  </g>
  
  <g transform="translate(620, 190)">
    <rect width="100" height="60" rx="6" fill="#ef4444" transform="skewY(-5deg)"/>
    <rect x="2" y="-2" width="100" height="60" rx="6" fill="#f87171"/>
    <text x="52" y="20" text-anchor="middle" fill="white" font-family="Arial" font-size="12">成本</text>
    <text x="52" y="40" text-anchor="middle" fill="white" font-family="Arial" font-size="18" font-weight="bold">¥45K</text>
  </g>
  
  <!-- 3D柱状图区域 -->
  <rect x="240" y="290" width="250" height="270" rx="8" fill="#ffffff" stroke="#e5e7eb"/>
  <text x="250" y="315" fill="#111827" font-family="Arial" font-size="16" font-weight="600">投放效果分析</text>
  
  <!-- 3D柱状图 -->
  <g transform="translate(260, 330)">
    <!-- 柱子1 -->
    <rect x="20" y="60" width="25" height="120" fill="#3b82f6" transform="skewX(-10deg)"/>
    <rect x="20" y="60" width="25" height="8" fill="#60a5fa" transform="skewX(-10deg)"/>
    <rect x="45" y="52" width="8" height="120" fill="#1d4ed8" transform="skewY(-20deg)"/>
    <text x="32" y="200" text-anchor="middle" fill="#6b7280" font-family="Arial" font-size="10">微信</text>
    
    <!-- 柱子2 -->
    <rect x="70" y="80" width="25" height="100" fill="#10b981" transform="skewX(-10deg)"/>
    <rect x="70" y="80" width="25" height="8" fill="#34d399" transform="skewX(-10deg)"/>
    <rect x="95" y="72" width="8" height="100" fill="#047857" transform="skewY(-20deg)"/>
    <text x="82" y="200" text-anchor="middle" fill="#6b7280" font-family="Arial" font-size="10">抖音</text>
    
    <!-- 柱子3 -->
    <rect x="120" y="100" width="25" height="80" fill="#f59e0b" transform="skewX(-10deg)"/>
    <rect x="120" y="100" width="25" height="8" fill="#fbbf24" transform="skewX(-10deg)"/>
    <rect x="145" y="92" width="8" height="80" fill="#d97706" transform="skewY(-20deg)"/>
    <text x="132" y="200" text-anchor="middle" fill="#6b7280" font-family="Arial" font-size="10">百度</text>
    
    <!-- 柱子4 -->
    <rect x="170" y="120" width="25" height="60" fill="#ef4444" transform="skewX(-10deg)"/>
    <rect x="170" y="120" width="25" height="8" fill="#f87171" transform="skewX(-10deg)"/>
    <rect x="195" y="112" width="8" height="60" fill="#dc2626" transform="skewY(-20deg)"/>
    <text x="182" y="200" text-anchor="middle" fill="#6b7280" font-family="Arial" font-size="10">其他</text>
  </g>
  
  <!-- 实时监控区域 -->
  <rect x="510" y="290" width="250" height="270" rx="8" fill="#ffffff" stroke="#e5e7eb"/>
  <text x="520" y="315" fill="#111827" font-family="Arial" font-size="16" font-weight="600">实时监控</text>
  
  <!-- 实时数据流 -->
  <rect x="520" y="330" width="230" height="30" rx="4" fill="#f3f4f6"/>
  <rect x="520" y="330" width="180" height="30" rx="4" fill="#3b82f6" fill-opacity="0.1"/>
  <text x="530" y="350" fill="#3b82f6" font-family="Arial" font-size="12">实时访问: 1,234 人在线</text>
  <circle cx="720" cy="345" r="4" fill="#10b981">
    <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <rect x="520" y="370" width="230" height="30" rx="4" fill="#f3f4f6"/>
  <rect x="520" y="370" width="150" height="30" rx="4" fill="#10b981" fill-opacity="0.1"/>
  <text x="530" y="390" fill="#10b981" font-family="Arial" font-size="12">新增订单: +23 单/分钟</text>
  <circle cx="720" cy="385" r="4" fill="#3b82f6">
    <animate attributeName="opacity" values="1;0.3;1" dur="1.5s" repeatCount="indefinite"/>
  </circle>
  
  <rect x="520" y="410" width="230" height="30" rx="4" fill="#f3f4f6"/>
  <rect x="520" y="410" width="200" height="30" rx="4" fill="#f59e0b" fill-opacity="0.1"/>
  <text x="530" y="430" fill="#f59e0b" font-family="Arial" font-size="12">系统负载: 67% CPU使用率</text>
  <circle cx="720" cy="425" r="4" fill="#f59e0b">
    <animate attributeName="opacity" values="1;0.3;1" dur="1s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 地图热力图模拟 -->
  <rect x="520" y="460" width="230" height="80" rx="4" fill="#f8fafc" stroke="#e5e7eb"/>
  <text x="530" y="480" fill="#6b7280" font-family="Arial" font-size="12">地域分布热力图</text>
  
  <!-- 模拟热力点 -->
  <circle cx="580" cy="500" r="8" fill="#ef4444" fill-opacity="0.6"/>
  <circle cx="620" cy="510" r="6" fill="#f59e0b" fill-opacity="0.6"/>
  <circle cx="660" cy="495" r="10" fill="#ef4444" fill-opacity="0.8"/>
  <circle cx="600" cy="520" r="4" fill="#10b981" fill-opacity="0.6"/>
  <circle cx="640" cy="525" r="5" fill="#3b82f6" fill-opacity="0.6"/>
</svg>
