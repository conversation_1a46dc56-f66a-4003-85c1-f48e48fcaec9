{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "jsxImportSource": "react", "incremental": true, "allowImportingTsExtensions": true, "customConditions": ["sui", "sui-pro"], "baseUrl": ".", "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"], "content-collections": ["./.content-collections/generated"], "compositions/*": ["../compositions/src/*"], "@saas-ui/react": ["../../packages/saas-ui-react/src/index.ts"], "@saas-ui/react/*": ["../../packages/saas-ui-react/src/components/*"], "@saas-ui/forms": ["../../packages/saas-ui-forms/src/index.ts"], "@saas-ui/charts": ["../../packages/saas-ui-charts/src/index.ts"], "@saas-ui/core": ["../../packages/saas-ui-core/src/index.ts"], "@saas-ui/core/utils": ["../../packages/saas-ui-core/src/utils/index.ts"], "@saas-ui/core/*": ["../../packages/saas-ui-core/src/components/*"], "@saas-ui/assets": ["../../packages/saas-ui-assets/src/index.ts"], "@saas-ui/hooks": ["../../packages/saas-ui-hooks/src/index.ts"], "@saas-ui/use-hotkeys": ["../../packages/saas-ui-use-hotkeys/src/index.ts"]}, "target": "ES2017"}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".velite", ".next/types/**/*.ts", "../compositions", "next.config.mjs"], "exclude": ["node_modules"]}