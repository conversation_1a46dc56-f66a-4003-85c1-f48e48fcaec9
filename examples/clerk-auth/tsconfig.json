{"compilerOptions": {"allowJs": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "jsx": "preserve", "lib": ["dom", "esnext"], "module": "esnext", "moduleResolution": "node", "noEmit": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "esnext", "allowSyntheticDefaultImports": true, "incremental": true, "plugins": [{"name": "next"}]}, "exclude": ["node_modules"], "include": ["**/*.ts", "**/*.tsx", ".next/types/**/*.ts"]}